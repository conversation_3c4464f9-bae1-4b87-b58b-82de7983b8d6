# Changelog

Todos los cambios notables en este proyecto serán documentados en este archivo.

El formato está basado en [Keep a Changelog](https://keepachangelog.com/es-ES/1.0.0/),
y este proyecto adhiere a [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- Sistema de control de versiones con Git
- Documentación completa del proyecto
- Archivos de licencia y contribución

## [1.2.0] - 2024-12-19

### Added
- **Fase de Arena 3D**: Entorno tridimensional para la batalla final
- **Sistema de Colisiones Mejorado**: Prevención de solapamientos entre equipos
- **Sistema de Victoria por Eliminación**: Solo la eliminación completa determina el ganador
- **Efectos Visuales 3D**: Sombras, perspectiva y iluminación ambiental
- **Muros Metálicos**: Decoraciones doradas en la arena

### Changed
- **Lógica de Victoria**: Eliminada la victoria por puntuación en la fase de arena
- **Renderizado**: Transición completa a modo arena 3D
- **Posicionamiento**: Equipos reposicionados en lados opuestos de la arena

### Fixed
- **Colisiones**: Prevención de movimientos a través de miembros del equipo contrario
- **Lógica de Combate**: Solo luchadores líderes pueden atacar
- **Sistema de Escudos**: Destrucción correcta después de 3 golpes

## [1.1.0] - 2024-12-19

### Added
- **Sistema de Combate**: Daño, XP y eliminación de personajes
- **Sucesión de Líderes**: Cambio automático cuando un líder es eliminado
- **Sistema de Knockback**: Retroceso de personajes atacados
- **Interacciones con Escudos**: Bloqueo de movimiento y ataques
- **Sistema de Eliminación de Equipos**: Victoria por eliminación completa

### Changed
- **Mecánicas de Colisión**: Sistema híbrido de prevención y resolución
- **Lógica de Movimiento**: Verificación de colisiones antes del movimiento
- **Sistema de Daño**: Reducción de XP y eliminación automática

### Fixed
- **Colisiones entre Equipos**: Prevención de solapamientos
- **Sistema de Power-ups**: Uso único por equipo
- **Lógica de Combate**: Condiciones correctas para ataques

## [1.0.0] - 2024-12-19

### Added
- **Sistema de Laberinto**: Generación procedural simétrica
- **Sistema de Equipos**: Dos equipos con 3 personajes cada uno
- **Personajes Únicos**: Mago, Arquero y Luchador
- **Sistema de Power-ups**: Escudos, flechas y romper muros
- **Fase de Central Room**: Temporizador de 10 segundos
- **Sistema de Puntuación**: XP basado en recolección de pellets
- **Interfaz de Usuario**: Paneles de estadísticas para ambos equipos

### Features
- **Generación de Laberinto**: Algoritmo de backtracking recursivo
- **Colocación Simétrica**: Power-ups y llaves en posiciones equilibradas
- **Sistema de Movimiento**: Control por turnos con formación en línea
- **Detección de Colisiones**: Con muros, power-ups y otros equipos
- **Renderizado Canvas**: Gráficos 2D con HTML5 Canvas

---

## Notas de Versión

### v1.2.0 - Arena 3D
Esta versión introduce la fase final del juego con un entorno 3D completamente nuevo. Los equipos se enfrentan en una arena cerrada donde solo la eliminación completa determina el ganador.

### v1.1.0 - Sistema de Combate
Implementación completa del sistema de combate con daño, XP y eliminación de personajes. Mejoras significativas en la lógica de colisiones y el sistema de equipos.

### v1.0.0 - Versión Base
Lanzamiento inicial del juego con todas las mecánicas básicas implementadas. Sistema completo de laberinto, equipos y power-ups.

