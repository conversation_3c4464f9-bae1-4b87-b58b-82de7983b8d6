class Maze {
    constructor(width, height) {
        // Ensure dimensions are odd for proper maze generation
        this.width = width % 2 === 0 ? width + 1 : width;
        this.height = height % 2 === 0 ? height + 1 : height;
        this.grid = [];
        this.centralRoomSize = 5; // Size of the central room
        this.centralRoomDoors = []; // Positions of the doors to the central room
        this.powerUpPairs = []; // Track pairs of symmetrical power-ups
        
        // Track which power-up types each team has collected
        this.collectedPowerUps = {
            1: [], // Team 1's collected power-up types
            2: []  // Team 2's collected power-up types
        };
        
        // Initialize the grid
        this.initializeGrid();
        
        // Generate the maze
        this.generateMaze();
        
        // Add pellets, power-ups, and keys
        this.addItems();
    }
    
    initializeGrid() {
        // Create a grid filled with walls
        for (let y = 0; y < this.height; y++) {
            const row = [];
            for (let x = 0; x < this.width; x++) {
                row.push({
                    isWall: true,
                    hasPellet: false,
                    hasPowerUp: false,
                    powerUpType: null,
                    hasKey: false,
                    isCentralRoomDoor: false
                });
            }
            this.grid.push(row);
        }
    }
    
    generateMaze() {
        // Create the central room
        this.createCentralRoom();
        
        // Start maze generation from two symmetric points
        const startX1 = 1;
        const startY1 = Math.floor(this.height / 2);
        const startX2 = this.width - 2;
        const startY2 = Math.floor(this.height / 2);
        
        // Create paths using recursive backtracking, ensuring symmetry
        this.recursiveBacktracking(startX1, startY1, startX2, startY2);
        
        // Ensure there are no dead ends
        this.removeDeadEnds();
        
        // Create doors to the central room
        this.createCentralRoomDoors();
    }
    
    createCentralRoom() {
        const centerX = Math.floor(this.width / 2);
        const centerY = Math.floor(this.height / 2);
        const halfSize = Math.floor(this.centralRoomSize / 2);
        
        // Create an empty room in the center
        for (let y = centerY - halfSize; y <= centerY + halfSize; y++) {
            for (let x = centerX - halfSize; x <= centerX + halfSize; x++) {
                if (y >= 0 && y < this.height && x >= 0 && x < this.width) {
                    // Make the central room walls
                    if (x === centerX - halfSize || x === centerX + halfSize || 
                        y === centerY - halfSize || y === centerY + halfSize) {
                        this.grid[y][x].isWall = true;
                    } else {
                        // Inside the room is empty
                        this.grid[y][x].isWall = false;
                    }
                }
            }
        }
    }
    
    createCentralRoomDoors() {
        const centerX = Math.floor(this.width / 2);
        const centerY = Math.floor(this.height / 2);
        const halfSize = Math.floor(this.centralRoomSize / 2);
        
        // Create two doors on opposite sides
        // Left door
        const leftDoorY = centerY;
        this.grid[leftDoorY][centerX - halfSize].isWall = false;
        this.grid[leftDoorY][centerX - halfSize].isCentralRoomDoor = true;
        this.centralRoomDoors.push({x: centerX - halfSize, y: leftDoorY});
        
        // Right door
        const rightDoorY = centerY;
        this.grid[rightDoorY][centerX + halfSize].isWall = false;
        this.grid[rightDoorY][centerX + halfSize].isCentralRoomDoor = true;
        this.centralRoomDoors.push({x: centerX + halfSize, y: rightDoorY});
    }
    
    recursiveBacktracking(x1, y1, x2, y2, visited = new Set()) {
        // Mark current cells as visited
        const key1 = `${x1},${y1}`;
        const key2 = `${x2},${y2}`;
        visited.add(key1);
        visited.add(key2);
        
        // Make current cells paths
        this.grid[y1][x1].isWall = false;
        this.grid[y2][x2].isWall = false;
        
        // Define possible directions (up, right, down, left)
        const directions = [
            {dx: 0, dy: -2}, // Up
            {dx: 2, dy: 0},  // Right
            {dx: 0, dy: 2},  // Down
            {dx: -2, dy: 0}  // Left
        ];
        
        // Shuffle directions for randomness
        this.shuffleArray(directions);
        
        // Try each direction
        for (const dir of directions) {
            const nx1 = x1 + dir.dx;
            const ny1 = y1 + dir.dy;
            const nx2 = x2 - dir.dx; // Symmetric point
            const ny2 = y2 - dir.dy; // Symmetric point
            
            // Check if both new positions are valid
            if (this.isValidPosition(nx1, ny1) && this.isValidPosition(nx2, ny2)) {
                const newKey1 = `${nx1},${ny1}`;
                const newKey2 = `${nx2},${ny2}`;
                
                // If both cells haven't been visited
                if (!visited.has(newKey1) && !visited.has(newKey2)) {
                    // Create passages by removing walls between current and new cells
                    this.grid[y1 + dir.dy/2][x1 + dir.dx/2].isWall = false;
                    this.grid[y2 - dir.dy/2][x2 - dir.dx/2].isWall = false;
                    
                    // Continue recursively
                    this.recursiveBacktracking(nx1, ny1, nx2, ny2, visited);
                }
            }
        }
    }
    
    removeDeadEnds() {
        let deadEndsRemoved = true;
        
        while (deadEndsRemoved) {
            deadEndsRemoved = false;
            
            for (let y = 1; y < this.height - 1; y++) {
                for (let x = 1; x < this.width - 1; x++) {
                    // Skip walls and central room
                    if (this.grid[y][x].isWall || this.isCentralRoom(x, y)) {
                        continue;
                    }
                    
                    // Count walls around this cell
                    let wallCount = 0;
                    if (this.grid[y-1][x].isWall) wallCount++;
                    if (this.grid[y+1][x].isWall) wallCount++;
                    if (this.grid[y][x-1].isWall) wallCount++;
                    if (this.grid[y][x+1].isWall) wallCount++;
                    
                    // If this is a dead end (3 walls around it)
                    if (wallCount === 3) {
                        // Find a symmetric cell
                        const symX = this.width - 1 - x;
                        const symY = this.height - 1 - y;
                        
                        // Remove a wall to connect to another path
                        const directions = [
                            {dx: 0, dy: -1}, // Up
                            {dx: 1, dy: 0},  // Right
                            {dx: 0, dy: 1},  // Down
                            {dx: -1, dy: 0}  // Left
                        ];
                        
                        this.shuffleArray(directions);
                        
                        for (const dir of directions) {
                            const nx = x + dir.dx;
                            const ny = y + dir.dy;
                            const symNx = symX - dir.dx;
                            const symNy = symY - dir.dy;
                            
                            // If there's a wall in this direction and it's not on the edge
                            if (this.isValidPosition(nx, ny) && this.grid[ny][nx].isWall &&
                                !this.isCentralRoom(nx, ny) && !this.isOnEdge(nx, ny)) {
                                
                                // Remove the wall
                                this.grid[ny][nx].isWall = false;
                                
                                // Also remove the symmetric wall
                                if (this.isValidPosition(symNx, symNy)) {
                                    this.grid[symNy][symNx].isWall = false;
                                }
                                
                                deadEndsRemoved = true;
                                break;
                            }
                        }
                    }
                }
            }
        }
    }
    
    addItems() {
        // Add pellets to all path cells
        for (let y = 0; y < this.height; y++) {
            for (let x = 0; x < this.width; x++) {
                if (!this.grid[y][x].isWall && !this.isCentralRoom(x, y)) {
                    this.grid[y][x].hasPellet = true;
                }
            }
        }
        
        // Add power-ups (3 types, symmetrically placed)
        const powerUpTypes = ['shield', 'arrow', 'break'];
        
        for (let i = 0; i < powerUpTypes.length; i++) {
            this.addSymmetricalPowerUps(powerUpTypes[i]);
        }
        
        // Add keys (one for each team)
        this.addSymmetricalKeys();
    }
    
    addSymmetricalPowerUps(powerUpType) {
        let placed = false;
        while (!placed) {
            // Choose a random position in the left half of the maze
            const x = Math.floor(Math.random() * (this.width / 2 - 2)) + 1;
            const y = Math.floor(Math.random() * (this.height - 2)) + 1;
            
            // Calculate the symmetrical position
            const symX = this.width - 1 - x;
            const symY = this.height - 1 - y;
            
            // Check if both positions are valid for power-ups
            if (!this.grid[y][x].isWall && !this.isCentralRoom(x, y) && 
                !this.grid[y][x].hasPowerUp && !this.grid[y][x].hasKey &&
                !this.grid[symY][symX].isWall && !this.isCentralRoom(symX, symY) && 
                !this.grid[symY][symX].hasPowerUp && !this.grid[symY][symX].hasKey) {
                
                // Place power-ups at both positions
                this.grid[y][x].hasPellet = false;
                this.grid[y][x].hasPowerUp = true;
                this.grid[y][x].powerUpType = powerUpType;
                this.grid[y][x].powerUpTeam = 1; // For team 1
                
                this.grid[symY][symX].hasPellet = false;
                this.grid[symY][symX].hasPowerUp = true;
                this.grid[symY][symX].powerUpType = powerUpType;
                this.grid[symY][symX].powerUpTeam = 2; // For team 2
                
                // Store the pair for tracking
                this.powerUpPairs.push({
                    type: powerUpType,
                    positions: [
                        {x: x, y: y, team: 1},
                        {x: symX, y: symY, team: 2}
                    ]
                });
                
                placed = true;
            }
        }
    }
    
    addSymmetricalKeys() {
        let placed = false;
        while (!placed) {
            // Choose a random position in the left half of the maze
            const x = Math.floor(Math.random() * (this.width / 2 - 2)) + 1;
            const y = Math.floor(Math.random() * (this.height - 2)) + 1;
            
            // Calculate the symmetrical position
            const symX = this.width - 1 - x;
            const symY = this.height - 1 - y;
            
            // Check if both positions are valid for keys
            if (!this.grid[y][x].isWall && !this.isCentralRoom(x, y) && 
                !this.grid[y][x].hasPowerUp && !this.grid[y][x].hasKey &&
                !this.grid[symY][symX].isWall && !this.isCentralRoom(symX, symY) && 
                !this.grid[symY][symX].hasPowerUp && !this.grid[symY][symX].hasKey) {
                
                // Place keys at both positions
                this.grid[y][x].hasPellet = false;
                this.grid[y][x].hasKey = true;
                
                this.grid[symY][symX].hasPellet = false;
                this.grid[symY][symX].hasKey = true;
                
                placed = true;
            }
        }
    }
    
    // Method to handle power-up consumption
    consumePowerUp(x, y, team) {
        const cell = this.getCell(x, y);
        if (!cell || !cell.hasPowerUp) return null;
        
        const powerUpType = cell.powerUpType;
        console.log(`Team ${team} attempting to collect ${powerUpType} power-up`);
        
        // Check if the team has already collected this type of power-up
        if (this.collectedPowerUps[team].includes(powerUpType)) {
            // Team already has this power-up type, cannot collect again
            console.log(`Team ${team} already has ${powerUpType} power-up`);
            return null;
        }
        
        // Mark this power-up type as collected by this team
        this.collectedPowerUps[team].push(powerUpType);
        console.log(`Team ${team} collected ${powerUpType} power-up - this will be permanent!`);
        
        // Remove the power-up from the grid
        cell.hasPowerUp = false;
        cell.powerUpType = null;
        cell.powerUpTeam = null;
        
        return {
            type: powerUpType,
            team: team,
            permanent: true // Indicate that this power-up is permanent
        };
    }
    
    isValidPosition(x, y) {
        return x > 0 && x < this.width - 1 && y > 0 && y < this.height - 1 && !this.isCentralRoom(x, y);
    }
    
    isValidMove(x, y) {
        // Check if the position is within bounds
        if (x < 0 || x >= this.width || y < 0 || y >= this.height) {
            return false;
        }
        
        // Check if the position is a wall
        if (this.grid[y][x].isWall) {
            return false;
        }
        
        // Check if it's a central room door (requires key)
        if (this.grid[y][x].isCentralRoomDoor) {
            // The door check will be handled by the game logic
            return true;
        }
        
        return true;
    }
    
    isCentralRoom(x, y) {
        const centerX = Math.floor(this.width / 2);
        const centerY = Math.floor(this.height / 2);
        const halfSize = Math.floor(this.centralRoomSize / 2);
        
        return x >= centerX - halfSize && x <= centerX + halfSize && 
               y >= centerY - halfSize && y <= centerY + halfSize;
    }
    
    isOnEdge(x, y) {
        return x === 0 || x === this.width - 1 || y === 0 || y === this.height - 1;
    }
    
    getCell(x, y) {
        if (x < 0 || x >= this.width || y < 0 || y >= this.height) {
            return null;
        }
        return this.grid[y][x];
    }
    
    render(ctx, cellSize) {
        for (let y = 0; y < this.height; y++) {
            for (let x = 0; x < this.width; x++) {
                const cell = this.grid[y][x];
                const cellX = x * cellSize;
                const cellY = y * cellSize;
                
                // Draw walls
                if (cell.isWall) {
                    ctx.fillStyle = '#333';
                    ctx.fillRect(cellX, cellY, cellSize, cellSize);
                } else {
                    // Draw path
                    ctx.fillStyle = '#000';
                    ctx.fillRect(cellX, cellY, cellSize, cellSize);
                    
                    // Draw central room floor with different color
                    if (this.isCentralRoom(x, y)) {
                        ctx.fillStyle = '#222';
                        ctx.fillRect(cellX, cellY, cellSize, cellSize);
                    }
                    
                    // Draw door
                    if (cell.isCentralRoomDoor) {
                        ctx.fillStyle = '#654321';
                        ctx.fillRect(cellX, cellY, cellSize, cellSize);
                    }
                    
                    // Draw pellet
                    if (cell.hasPellet) {
                        ctx.fillStyle = '#FFF';
                        ctx.beginPath();
                        ctx.arc(cellX + cellSize / 2, cellY + cellSize / 2, cellSize * 0.1, 0, Math.PI * 2);
                        ctx.fill();
                    }
                    
                    // Draw power-up
                    if (cell.hasPowerUp) {
                        let color;
                        switch (cell.powerUpType) {
                            case 'shield':
                                color = '#00FFFF'; // Cyan for mage shield
                                break;
                            case 'arrow':
                                color = '#FF00FF'; // Magenta for archer arrows
                                break;
                            case 'break':
                                color = '#FFFF00'; // Yellow for fighter break
                                break;
                        }
                        
                        // Add team-specific border
                        const teamColor = cell.powerUpTeam === 1 ? '#0000FF' : '#FF0000';
                        
                        if (cell.powerUpType === 'break') {
                            // Draw a sword for fighter power-up
                            const centerX = cellX + cellSize / 2;
                            const centerY = cellY + cellSize / 2;
                            const swordLength = cellSize * 0.3;
                            const handleLength = cellSize * 0.1;
                            const swordWidth = cellSize * 0.03;
                            const guardWidth = cellSize * 0.1;
                            const guardHeight = cellSize * 0.03;
                            
                            // Draw sword blade
                            ctx.fillStyle = '#CCCCCC'; // Silver blade
                            ctx.beginPath();
                            ctx.moveTo(centerX, centerY - swordLength);
                            ctx.lineTo(centerX + swordWidth, centerY - swordLength + swordWidth * 2);
                            ctx.lineTo(centerX + swordWidth, centerY - guardHeight);
                            ctx.lineTo(centerX - swordWidth, centerY - guardHeight);
                            ctx.lineTo(centerX - swordWidth, centerY - swordLength + swordWidth * 2);
                            ctx.closePath();
                            ctx.fill();
                            
                            // Draw sword guard
                            ctx.fillStyle = '#FFD700'; // Gold guard
                            ctx.beginPath();
                            ctx.rect(centerX - guardWidth / 2, centerY - guardHeight, guardWidth, guardHeight);
                            ctx.fill();
                            
                            // Draw sword handle
                            ctx.fillStyle = '#8B4513'; // Brown handle
                            ctx.beginPath();
                            ctx.rect(centerX - swordWidth, centerY, swordWidth * 2, handleLength);
                            ctx.fill();
                            
                            // Draw team-colored border around the sword
                            ctx.strokeStyle = teamColor;
                            ctx.lineWidth = 2;
                            ctx.beginPath();
                            ctx.arc(centerX, centerY, cellSize * 0.2, 0, Math.PI * 2);
                            ctx.stroke();
                        } else if (cell.powerUpType === 'arrow') {
                            // Draw an arrow for archer power-up
                            const centerX = cellX + cellSize / 2;
                            const centerY = cellY + cellSize / 2;
                            const arrowLength = cellSize * 0.3;
                            const arrowWidth = cellSize * 0.02;
                            const arrowheadWidth = cellSize * 0.06;
                            const arrowheadLength = cellSize * 0.06;
                            const fletchingWidth = cellSize * 0.05;
                            const fletchingLength = cellSize * 0.04;
                            
                            // Draw arrow shaft
                            ctx.fillStyle = '#8B4513'; // Brown shaft
                            ctx.beginPath();
                            ctx.rect(centerX - arrowWidth / 2, centerY - arrowLength / 2, arrowWidth, arrowLength);
                            ctx.fill();
                            
                            // Draw arrowhead
                            ctx.fillStyle = '#CCCCCC'; // Silver arrowhead
                            ctx.beginPath();
                            ctx.moveTo(centerX, centerY - arrowLength / 2 - arrowheadLength);
                            ctx.lineTo(centerX + arrowheadWidth / 2, centerY - arrowLength / 2);
                            ctx.lineTo(centerX - arrowheadWidth / 2, centerY - arrowLength / 2);
                            ctx.closePath();
                            ctx.fill();
                            
                            // Draw fletching
                            ctx.fillStyle = '#FF00FF'; // Magenta fletching
                            ctx.beginPath();
                            ctx.moveTo(centerX, centerY + arrowLength / 2 + fletchingLength);
                            ctx.lineTo(centerX + fletchingWidth / 2, centerY + arrowLength / 2);
                            ctx.lineTo(centerX - fletchingWidth / 2, centerY + arrowLength / 2);
                            ctx.closePath();
                            ctx.fill();
                            
                            // Draw team-colored border around the arrow
                            ctx.strokeStyle = teamColor;
                            ctx.lineWidth = 2;
                            ctx.beginPath();
                            ctx.arc(centerX, centerY, cellSize * 0.2, 0, Math.PI * 2);
                            ctx.stroke();
                        } else if (cell.powerUpType === 'shield') {
                            // Draw a shield for mage power-up
                            const centerX = cellX + cellSize / 2;
                            const centerY = cellY + cellSize / 2;
                            const shieldWidth = cellSize * 0.25;
                            const shieldHeight = cellSize * 0.35;
                            
                            // Draw shield base
                            ctx.fillStyle = '#00FFFF'; // Cyan shield
                            ctx.beginPath();
                            ctx.moveTo(centerX, centerY - shieldHeight / 2);
                            ctx.lineTo(centerX + shieldWidth / 2, centerY - shieldHeight / 3);
                            ctx.lineTo(centerX + shieldWidth / 2, centerY + shieldHeight / 3);
                            ctx.lineTo(centerX, centerY + shieldHeight / 2);
                            ctx.lineTo(centerX - shieldWidth / 2, centerY + shieldHeight / 3);
                            ctx.lineTo(centerX - shieldWidth / 2, centerY - shieldHeight / 3);
                            ctx.closePath();
                            ctx.fill();
                            
                            // Draw shield border
                            ctx.strokeStyle = '#0088AA'; // Darker cyan border
                            ctx.lineWidth = 1;
                            ctx.beginPath();
                            ctx.moveTo(centerX, centerY - shieldHeight / 2);
                            ctx.lineTo(centerX + shieldWidth / 2, centerY - shieldHeight / 3);
                            ctx.lineTo(centerX + shieldWidth / 2, centerY + shieldHeight / 3);
                            ctx.lineTo(centerX, centerY + shieldHeight / 2);
                            ctx.lineTo(centerX - shieldWidth / 2, centerY + shieldHeight / 3);
                            ctx.lineTo(centerX - shieldWidth / 2, centerY - shieldHeight / 3);
                            ctx.closePath();
                            ctx.stroke();
                            
                            // Draw shield emblem
                            ctx.fillStyle = teamColor; // Team color
                            ctx.beginPath();
                            ctx.arc(centerX, centerY, cellSize * 0.06, 0, Math.PI * 2);
                            ctx.fill();
                            
                            // Draw team-colored border around the shield
                            ctx.strokeStyle = teamColor;
                            ctx.lineWidth = 2;
                            ctx.beginPath();
                            ctx.arc(centerX, centerY, cellSize * 0.2, 0, Math.PI * 2);
                            ctx.stroke();
                        }
                    }
                    
                    // Draw key
                    if (cell.hasKey) {
                        ctx.fillStyle = '#FFD700';
                        
                        // Draw key shape
                        ctx.beginPath();
                        ctx.arc(cellX + cellSize * 0.4, cellY + cellSize / 2, cellSize * 0.15, 0, Math.PI * 2);
                        ctx.fill();
                        
                        ctx.fillRect(cellX + cellSize * 0.5, cellY + cellSize * 0.4, cellSize * 0.3, cellSize * 0.2);
                        
                        ctx.beginPath();
                        ctx.moveTo(cellX + cellSize * 0.7, cellY + cellSize * 0.4);
                        ctx.lineTo(cellX + cellSize * 0.7, cellY + cellSize * 0.3);
                        ctx.lineTo(cellX + cellSize * 0.8, cellY + cellSize * 0.3);
                        ctx.lineTo(cellX + cellSize * 0.8, cellY + cellSize * 0.4);
                        ctx.fill();
                    }
                }
            }
        }
    }
    
    shuffleArray(array) {
        for (let i = array.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [array[i], array[j]] = [array[j], array[i]];
        }
    }
    
    isCentralRoomWall(x, y) {
        const centerX = Math.floor(this.width / 2);
        const centerY = Math.floor(this.height / 2);
        const halfSize = Math.floor(this.centralRoomSize / 2);
        
        // Check if the position is on the border of the central room
        if (x >= centerX - halfSize && x <= centerX + halfSize &&
            y >= centerY - halfSize && y <= centerY + halfSize) {
            
            // Check if it's on the exact border of the central room
            return (x === centerX - halfSize || x === centerX + halfSize ||
                    y === centerY - halfSize || y === centerY + halfSize);
        }
        
        return false;
    }

    closeCentralRoomDoors() {
        // Turn all central room doors into walls
        for (const door of this.centralRoomDoors) {
            const cell = this.grid[door.y][door.x];
            cell.isWall = true;
            cell.isCentralRoomDoor = false;
        }
        
        // Clear the doors array
        this.centralRoomDoors = [];
        
        // Make sure all central room walls are solid
        const centerX = Math.floor(this.width / 2);
        const centerY = Math.floor(this.height / 2);
        const halfSize = Math.floor(this.centralRoomSize / 2);
        
        // Reinforce all central room walls
        for (let y = centerY - halfSize; y <= centerY + halfSize; y++) {
            for (let x = centerX - halfSize; x <= centerX + halfSize; x++) {
                if (x === centerX - halfSize || x === centerX + halfSize || 
                    y === centerY - halfSize || y === centerY + halfSize) {
                    this.grid[y][x].isWall = true;
                }
            }
        }
    }
} 