class PowerUp {
    constructor(type, x, y) {
        this.type = type; // 'shield', 'arrow', or 'break'
        this.x = x;
        this.y = y;
        this.collected = false;
        this.active = false;
        
        // Set visual properties based on type
        switch(type) {
            case 'shield':
                this.color = '#00FFFF'; // Cyan for mage shield
                break;
            case 'arrow':
                this.color = '#FF00FF'; // Magenta for archer arrows
                break;
            case 'break':
                this.color = '#FFFF00'; // Yellow for fighter break
                break;
        }
    }
    
    collect() {
        if (!this.collected) {
            this.collected = true;
            return true;
        }
        return false;
    }
    
    activate() {
        if (this.collected && !this.active) {
            this.active = true;
            return true;
        }
        return false;
    }
    
    deactivate() {
        this.active = false;
    }
    
    render(ctx, cellSize) {
        if (this.collected) return;
        
        const x = this.x * cellSize;
        const y = this.y * cellSize;
        
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(x + cellSize / 2, y + cellSize / 2, cellSize * 0.2, 0, Math.PI * 2);
        ctx.fill();
        
        // Draw a pulsing effect
        const pulseSize = 0.25 + Math.sin(Date.now() / 200) * 0.05;
        ctx.strokeStyle = this.color;
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.arc(x + cellSize / 2, y + cellSize / 2, cellSize * pulseSize, 0, Math.PI * 2);
        ctx.stroke();
    }
    
    // Shield ability for mage
    createShield(team, direction) {
        if (this.type !== 'shield' || !this.active) return null;
        
        return {
            type: 'shield',
            team: team,
            direction: direction,
            duration: 300, // 5 seconds at 60fps
            strength: 3 // Can block 3 hits
        };
    }
    
    // Arrow ability for archer
    shootArrow(team, x, y, direction, arrowCount) {
        if (this.type !== 'arrow' || !this.active || arrowCount <= 0) return null;
        
        // Calculate arrow trajectory based on direction
        let dx = 0, dy = 0;
        switch(direction) {
            case 'up': dy = -1; break;
            case 'down': dy = 1; break;
            case 'left': dx = -1; break;
            case 'right': dx = 1; break;
        }
        
        return {
            type: 'arrow',
            team: team,
            x: x,
            y: y,
            dx: dx,
            dy: dy,
            distance: 0,
            maxDistance: 5 // Arrow can travel 5 cells
        };
    }
    
    // Break ability for fighter
    breakWall(team, x, y, direction) {
        if (this.type !== 'break' || !this.active) return null;
        
        // Calculate target position based on direction
        let targetX = x, targetY = y;
        switch(direction) {
            case 'up': targetY--; break;
            case 'down': targetY++; break;
            case 'left': targetX--; break;
            case 'right': targetX++; break;
        }
        
        return {
            type: 'break',
            team: team,
            x: targetX,
            y: targetY
        };
    }
} 