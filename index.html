<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Amazed - <PERSON>&<PERSON> Pacman</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background-color: #222;
            font-family: Arial, sans-serif;
            color: white;
        }
        .game-wrapper {
            display: flex;
            flex-direction: row;
            align-items: flex-start;
            gap: 20px;
            justify-content: center;
            width: 100%;
            max-width: 1200px;
        }
        #game-container {
            position: relative;
            flex-shrink: 0;
        }
        canvas {
            border: 2px solid #444;
            background-color: #000;
        }
        .stats-panel {
            background-color: rgba(0, 0, 0, 0.7);
            border: 2px solid #444;
            border-radius: 5px;
            padding: 15px;
            width: 200px;
            min-height: 400px;
            flex-shrink: 0;
        }
        .team-header {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid #555;
        }
        .team-blue {
            color: #00AAFF;
        }
        .team-red {
            color: #FF5555;
        }
        .character-stats {
            margin-bottom: 8px;
            padding: 5px;
            border-radius: 3px;
        }
        .character-stats.leader {
            background-color: rgba(255, 215, 0, 0.2);
            border-left: 3px solid #FFD700;
        }
        .character-name {
            font-weight: bold;
            margin-bottom: 3px;
        }
        .character-xp {
            font-size: 14px;
        }
        .team-total {
            margin-top: 10px;
            font-weight: bold;
            border-top: 1px solid #555;
            padding-top: 5px;
        }
        .power-up {
            font-size: 14px;
        }
        .power-up .active {
            color: #FFFF00;
            font-weight: bold;
        }
        
        /* Split the stats panels */
        .blue-stats {
            margin-right: 0;
        }
        .red-stats {
            margin-left: 0;
        }
    </style>
</head>
<body>
    <div class="game-wrapper">
        <!-- Blue Team Stats (Left Side) -->
        <div class="stats-panel blue-stats">
            <div class="team-header team-blue">Team Blue</div>
            
            <div id="blue-mage" class="character-stats">
                <div class="character-name">Mage</div>
                <div class="character-xp">XP: <span id="blue-mage-xp">0</span></div>
                <div class="power-up">Power-up: <span id="blue-mage-powerup">No</span></div>
            </div>
            
            <div id="blue-archer" class="character-stats">
                <div class="character-name">Archer</div>
                <div class="character-xp">XP: <span id="blue-archer-xp">0</span></div>
                <div class="character-xp">Arrows: <span id="blue-archer-arrows">0</span></div>
                <div class="power-up">Power-up: <span id="blue-archer-powerup">No</span></div>
            </div>
            
            <div id="blue-fighter" class="character-stats">
                <div class="character-name">Fighter</div>
                <div class="character-xp">XP: <span id="blue-fighter-xp">0</span></div>
                <div class="power-up">Power-up: <span id="blue-fighter-powerup">No</span></div>
            </div>
            
            <div class="team-total">
                Total XP: <span id="blue-total-xp">0</span>
            </div>
            <div class="team-total">
                Current Leader: <span id="blue-leader">Mage</span>
            </div>
        </div>

        <!-- Game Canvas (Center) -->
        <div id="game-container">
            <canvas id="gameCanvas"></canvas>
        </div>
        
        <!-- Red Team Stats (Right Side) -->
        <div class="stats-panel red-stats">
            <div class="team-header team-red">Team Red</div>
            
            <div id="red-mage" class="character-stats">
                <div class="character-name">Mage</div>
                <div class="character-xp">XP: <span id="red-mage-xp">0</span></div>
                <div class="power-up">Power-up: <span id="red-mage-powerup">No</span></div>
            </div>
            
            <div id="red-archer" class="character-stats">
                <div class="character-name">Archer</div>
                <div class="character-xp">XP: <span id="red-archer-xp">0</span></div>
                <div class="character-xp">Arrows: <span id="red-archer-arrows">0</span></div>
                <div class="power-up">Power-up: <span id="red-archer-powerup">No</span></div>
            </div>
            
            <div id="red-fighter" class="character-stats">
                <div class="character-name">Fighter</div>
                <div class="character-xp">XP: <span id="red-fighter-xp">0</span></div>
                <div class="power-up">Power-up: <span id="red-fighter-powerup">No</span></div>
            </div>
            
            <div class="team-total">
                Total XP: <span id="red-total-xp">0</span>
            </div>
            <div class="team-total">
                Current Leader: <span id="red-leader">Mage</span>
            </div>
        </div>
    </div>

    <script src="src/game.js"></script>
    <script src="src/maze.js"></script>
    <script src="src/team.js"></script>
    <script src="src/character.js"></script>
    <script src="src/powerup.js"></script>
    <script src="src/main.js"></script>
</body>
</html> 