# 🎮 Amazed - Maze Battle Game

Un emocionante juego de laberinto multijugador donde dos equipos compiten para alcanzar la sala central y luchar en una arena 3D.

## 🚀 Características

### Mecánicas de Juego
- **Laberinto Generado Proceduralmente**: Cada partida es única
- **Sistema de Equipos**: Dos equipos con 3 personajes cada uno
- **Personajes Únicos**:
  - **Mago**: Crea escudos protectores
  - **Arquero**: Dispara flechas a distancia
  - **Luchador**: Rompe muros y ataca cuerpo a cuerpo

### Fases del Juego
1. **Fase de Exploración**: Los equipos exploran el laberinto recolectando power-ups
2. **Fase de Central Room**: El primer equipo en llegar activa un temporizador de 10 segundos
3. **Fase de Arena**: Cuando ambos equipos entran, comienza la batalla final en 3D

### Sistema de Power-ups
- **Escudo**: Absorbe 3 golpes antes de romperse
- **Flecha**: Permite al arquero disparar flechas
- **Romper**: Permite al luchador romper muros

## 🎯 Objetivo
Eliminar completamente al equipo contrario para ganar. La victoria se logra a través de:
- Eliminación de todos los miembros del equipo enemigo
- Llegar primero a la sala central (si el otro equipo no llega en 10 segundos)

## 🎮 Controles

### Equipo Azul (Team 1)
- **Movimiento**: WASD
- **Habilidad Especial**: E
- **Cambiar Líder**: Q

### Equipo Rojo (Team 2)
- **Movimiento**: Flechas
- **Habilidad Especial**: Espacio
- **Cambiar Líder**: Enter

## 🏗️ Estructura del Proyecto

```
src/
├── main.js          # Punto de entrada principal
├── game.js          # Lógica principal del juego
├── maze.js          # Generación y gestión del laberinto
├── team.js          # Gestión de equipos y personajes
├── character.js     # Clases de personajes
└── index.html       # Interfaz del juego
```

## 🚀 Instalación

1. Clona el repositorio:
```bash
git clone [URL_DEL_REPOSITORIO]
cd Amazed
```

2. Abre `index.html` en tu navegador web

## 🛠️ Tecnologías Utilizadas

- **HTML5 Canvas**: Renderizado del juego
- **JavaScript ES6+**: Lógica del juego
- **CSS3**: Estilos y animaciones
- **Git**: Control de versiones

## 📝 Historial de Versiones

### v1.0.0 - Versión Base
- Sistema de laberinto básico
- Personajes y equipos
- Power-ups básicos

### v1.1.0 - Sistema de Combate
- Sistema de daño y XP
- Eliminación de personajes
- Sucesión de líderes

### v1.2.0 - Arena 3D
- Fase de arena con entorno 3D
- Colisiones mejoradas
- Sistema de victoria por eliminación

## 🤝 Contribuir

1. Haz fork del proyecto
2. Crea una rama para tu feature (`git checkout -b feature/AmazingFeature`)
3. Commit tus cambios (`git commit -m 'Add some AmazingFeature'`)
4. Push a la rama (`git push origin feature/AmazingFeature`)
5. Abre un Pull Request

## 📄 Licencia

Este proyecto está bajo la Licencia MIT. Ver el archivo `LICENSE` para más detalles.

## 👥 Autores

- **Desarrollador Principal**: [Tu Nombre]
- **Contribuidores**: [Lista de contribuidores]

## 🐛 Reportar Bugs

Si encuentras algún bug, por favor:
1. Revisa si ya existe un issue similar
2. Crea un nuevo issue con:
   - Descripción detallada del problema
   - Pasos para reproducirlo
   - Comportamiento esperado vs. actual
   - Información del sistema (navegador, SO)

## 💡 Ideas Futuras

- [ ] Modo multijugador en red
- [ ] Más tipos de power-ups
- [ ] Diferentes mapas y temáticas
- [ ] Sistema de rankings
- [ ] Modo espectador
- [ ] Personalización de personajes

---

**¡Disfruta jugando Amazed! 🎮✨** 