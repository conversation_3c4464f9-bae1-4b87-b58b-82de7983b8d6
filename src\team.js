class Team {
    constructor(teamNumber, startX, startY) {
        this.teamNumber = teamNumber; // 1 or 2
        this.x = startX;
        this.y = startY;
        this.direction = teamNumber === 1 ? 'right' : 'left'; // Initial direction
        this.score = 0;
        this.hasKey = false;
        this.isMoving = false; // Flag to prevent multiple moves at once
        
        // Create the three characters
        this.characters = [
            new Character('mage', teamNumber),
            new Character('archer', teamNumber),
            new Character('fighter', teamNumber)
        ];
        
        // Start with mage as leader
        this.leaderIndex = 0;
        
        // Store previous positions for followers
        this.positionHistory = [];
        for (let i = 0; i < 2; i++) {
            // Initialize followers to be behind the leader based on team direction
            let followerX = startX;
            let followerY = startY;
            
            if (teamNumber === 1) {
                followerX -= (i + 1); // Team 1 starts facing right, so followers are to the left
            } else {
                followerX += (i + 1); // Team 2 starts facing left, so followers are to the right
            }
            
            this.positionHistory.push({ x: followerX, y: followerY });
        }
    }
    
    get leader() {
        return this.characters[this.leaderIndex];
    }
    
    changeLeader() {
        this.leaderIndex = (this.leaderIndex + 1) % this.characters.length;
        return this.leader.type;
    }
    
    move(direction, maze) {
        // Prevent multiple moves at once
        if (this.isMoving) return false;
        
        // Store the current position
        const prevX = this.x;
        const prevY = this.y;
        
        // Calculate new position based on direction
        let newX = this.x;
        let newY = this.y;
        
        switch(direction) {
            case 'up':
                newY--;
                break;
            case 'down':
                newY++;
                break;
            case 'left':
                newX--;
                break;
            case 'right':
                newX++;
                break;
        }
        
        // Check if the new position is valid (not a wall)
        if (maze.isValidMove(newX, newY)) {
            // Update position history for followers
            this.positionHistory.unshift({ x: this.x, y: this.y });
            if (this.positionHistory.length > 2) { // Keep only positions needed for followers
                this.positionHistory.pop();
            }
            
            // Update leader position
            this.x = newX;
            this.y = newY;
            this.direction = direction;
            
            // Set moving flag to prevent multiple moves
            this.isMoving = true;
            
            // Reset moving flag after a short delay (to prevent rapid movement)
            setTimeout(() => {
                this.isMoving = false;
            }, 150);
            
            return true;
        }
        
        return false;
    }
    
    collectItem(maze) {
        const cell = maze.getCell(this.x, this.y);
        
        if (cell.hasPellet) {
            // Collect pellet (XP)
            cell.hasPellet = false;
            
            // Add XP to team total
            this.score += 10;
            
            // Add XP to the current leader
            this.leader.collectXp(10);
            
            return 'pellet';
        } else if (cell.hasPowerUp) {
            // Use the new consumePowerUp method to handle power-up collection
            const powerUpResult = maze.consumePowerUp(this.x, this.y, this.teamNumber);
            
            if (powerUpResult) {
                // Find the character that should receive this power-up
                const powerUpType = powerUpResult.type;
                const targetCharacter = this.characters.find(char => {
                    if (powerUpType === 'shield' && char.type === 'mage') return true;
                    if (powerUpType === 'arrow' && char.type === 'archer') return true;
                    if (powerUpType === 'break' && char.type === 'fighter') return true;
                    return false;
                });
                
                if (targetCharacter) {
                    // Activate the power-up permanently
                    targetCharacter.activatePowerUp();
                    console.log(`Team ${this.teamNumber} ${targetCharacter.type} acquired permanent ${powerUpType} power-up!`);
                    return `powerUp-${powerUpType}`;
                }
            }
        } else if (cell.hasKey) {
            // Collect key
            cell.hasKey = false;
            this.hasKey = true;
            return 'key';
        }
        
        return null;
    }
    
    useSpecialAbility(game) {
        return this.leader.useSpecialAbility(game, this.direction);
    }
    
    canArcherShoot() {
        // Check if the archer is the leader and has the power-up active
        const archer = this.characters.find(char => char.type === 'archer');
        
        // Debug log to help diagnose the issue
        console.log(`Team ${this.teamNumber} archer check:`, {
            isArcherLeader: this.leader.type === 'archer',
            powerUpActive: archer.powerUpActive,
            arrowCount: archer.arrowCount
        });
        
        return (
            this.leader.type === 'archer' && 
            archer.powerUpActive && 
            archer.arrowCount > 0
        );
    }
    
    shootArrow(game) {
        const archer = this.characters.find(char => char.type === 'archer');
        console.log(`Team ${this.teamNumber} attempting to shoot arrow:`, {
            isArcherLeader: this.leader.type === 'archer',
            arrowCount: archer.arrowCount,
            powerUpActive: archer.powerUpActive
        });
        
        if (this.canArcherShoot()) {
            // Reduce arrow count
            archer.arrowCount--;
            
            console.log(`Team ${this.teamNumber} archer shooting arrow. Remaining arrows: ${archer.arrowCount}`);
            
            // Return arrow information
            return {
                team: this.teamNumber,
                direction: this.direction,
                x: this.x,
                y: this.y
            };
        }
        
        console.log(`Team ${this.teamNumber} cannot shoot arrow`);
        return null;
    }
    
    getCharacterAtPosition(x, y) {
        // Check if leader is at this position
        if (this.x === x && this.y === y) {
            return this.leader;
        }
        
        // Check if any follower is at this position
        for (let i = 0; i < this.positionHistory.length; i++) {
            const pos = this.positionHistory[i];
            if (pos.x === x && pos.y === y) {
                const followerIndex = (this.leaderIndex + i + 1) % 3;
                return this.characters[followerIndex];
            }
        }
        
        return null;
    }
    
    update() {
        // Update all characters
        this.characters.forEach(character => {
            character.updatePowerUp();
        });
    }
    
    render(ctx, cellSize) {
        // Render leader at current position
        const leaderX = this.x * cellSize;
        const leaderY = this.y * cellSize;
        
        // Draw leader
        if (this.characters.length > 0) {
            this.characters[this.leaderIndex].render(
                ctx, 
                leaderX, 
                leaderY, 
                cellSize, 
                true
            );
            
            // Draw followers
            let followerCount = 0;
            for (let i = 0; i < this.characters.length - 1; i++) {
                const followerIndex = (this.leaderIndex + i + 1) % this.characters.length;
                const followerPos = this.positionHistory[followerCount];
                
                if (followerPos) {
                    const followerX = followerPos.x * cellSize;
                    const followerY = followerPos.y * cellSize;
                    
                    this.characters[followerIndex].render(
                        ctx, 
                        followerX, 
                        followerY, 
                        cellSize, 
                        false
                    );
                    followerCount++;
                }
            }
        }
        
        // Draw key indicator if the team has the key
        if (this.hasKey) {
            ctx.fillStyle = '#FFD700';
            ctx.beginPath();
            ctx.arc(leaderX + cellSize / 2, leaderY + cellSize / 2, cellSize * 0.1, 0, Math.PI * 2);
            ctx.fill();
        }
    }

    getTeamPositions() {
        return this.characters.map(char => ({
            type: char.type,
            x: this.x + this.getCharacterOffset(char),
            y: this.y
        }));
    }

    getCharacterOffset(character) {
        const index = this.characters.indexOf(character);
        return index - this.characters.indexOf(this.leader);
    }

    removeCharacter(type) {
        const index = this.characters.findIndex(char => char.type === type);
        if (index !== -1) {
            // If removing the leader, change leader first
            if (index === this.leaderIndex) {
                // Find the next available character to be leader
                const remainingChars = this.characters.filter((_, i) => i !== index);
                if (remainingChars.length > 0) {
                    // Set leader index to the next available character
                    this.leaderIndex = (index + 1) % this.characters.length;
                    // Adjust leader index if it's now invalid
                    if (this.leaderIndex >= remainingChars.length) {
                        this.leaderIndex = 0;
                    }
                }
            } else if (index < this.leaderIndex) {
                // If removing a character before the leader, adjust leader index
                this.leaderIndex--;
            }
            
            // Remove the character
            this.characters.splice(index, 1);
            
            // Update team score
            this.updateTeamScore();
        }
    }

    moveCharacter(character, direction, maze) {
        const offset = this.getCharacterOffset(character);
        let newX = this.x + offset;
        let newY = this.y;
        
        switch(direction) {
            case 'up': newY--; break;
            case 'down': newY++; break;
            case 'left': newX--; break;
            case 'right': newX++; break;
        }
        
        if (maze.isValidMove(newX, newY)) {
            this.x = newX - offset;
            this.y = newY;
            return true;
        }
        return false;
    }

    updateTeamScore() {
        this.score = this.characters.reduce((total, char) => total + char.xp, 0);
    }
} 