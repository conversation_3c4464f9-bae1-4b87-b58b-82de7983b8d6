class Character {
    constructor(type, team) {
        this.type = type; // 'mage', 'archer', or 'fighter'
        this.team = team; // 1 or 2
        this.xp = 0;
        this.powerUpActive = false;
        
        // Set character-specific properties
        switch(type) {
            case 'mage':
                this.color = team === 1 ? '#00AAFF' : '#FF5555';
                this.specialAbility = 'shield';
                break;
            case 'archer':
                this.color = team === 1 ? '#0055FF' : '#FF0000';
                this.specialAbility = 'shoot';
                this.arrowCount = 0;
                break;
            case 'fighter':
                this.color = team === 1 ? '#0000AA' : '#AA0000';
                this.specialAbility = 'break';
                break;
        }
    }

    render(ctx, x, y, cellSize, isLeader) {
        const radius = cellSize * 0.4; // Slightly larger character
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(x + cellSize / 2, y + cellSize / 2, radius, 0, Math.PI * 2);
        ctx.fill();
        
        // Draw character type indicator
        ctx.fillStyle = '#FFF';
        ctx.font = '12px Arial';
        let letter = '';
        switch(this.type) {
            case 'mage': letter = 'M'; break;
            case 'archer': letter = 'A'; break;
            case 'fighter': letter = 'F'; break;
        }
        
        // Center the text
        const textWidth = ctx.measureText(letter).width;
        ctx.fillText(letter, x + cellSize / 2 - textWidth / 2, y + cellSize / 2 + 4);
        
        // Draw a crown for the leader
        if (isLeader) {
            ctx.fillStyle = '#FFD700';
            ctx.beginPath();
            ctx.moveTo(x + cellSize / 2 - radius / 2, y + cellSize / 2 - radius - 2);
            ctx.lineTo(x + cellSize / 2 + radius / 2, y + cellSize / 2 - radius - 2);
            ctx.lineTo(x + cellSize / 2 + radius / 3, y + cellSize / 2 - radius + 3);
            ctx.lineTo(x + cellSize / 2, y + cellSize / 2 - radius - 2);
            ctx.lineTo(x + cellSize / 2 - radius / 3, y + cellSize / 2 - radius + 3);
            ctx.closePath();
            ctx.fill();
        }
        
        // Draw power-up indicator if active
        if (this.powerUpActive) {
            if (this.type === 'fighter') {
                // Draw a sword for fighter power-up
                const swordLength = radius * 1.5;
                const handleLength = radius * 0.5;
                const swordWidth = radius * 0.15;
                const guardWidth = radius * 0.5;
                const guardHeight = radius * 0.15;
                
                // Sword position
                const swordX = x + cellSize / 2;
                const swordY = y + cellSize / 2;
                
                // Draw sword blade
                ctx.fillStyle = '#CCCCCC'; // Silver blade
                ctx.beginPath();
                ctx.moveTo(swordX, swordY - swordLength);
                ctx.lineTo(swordX + swordWidth, swordY - swordLength + swordWidth * 2);
                ctx.lineTo(swordX + swordWidth, swordY - guardHeight);
                ctx.lineTo(swordX - swordWidth, swordY - guardHeight);
                ctx.lineTo(swordX - swordWidth, swordY - swordLength + swordWidth * 2);
                ctx.closePath();
                ctx.fill();
                
                // Draw sword guard
                ctx.fillStyle = '#FFD700'; // Gold guard
                ctx.beginPath();
                ctx.rect(swordX - guardWidth / 2, swordY - guardHeight, guardWidth, guardHeight);
                ctx.fill();
                
                // Draw sword handle
                ctx.fillStyle = '#8B4513'; // Brown handle
                ctx.beginPath();
                ctx.rect(swordX - swordWidth, swordY, swordWidth * 2, handleLength);
                ctx.fill();
            } else if (this.type === 'archer') {
                // Draw an arrow for archer power-up
                const arrowLength = radius * 1.5;
                const arrowWidth = radius * 0.1;
                const arrowheadWidth = radius * 0.3;
                const arrowheadLength = radius * 0.3;
                const fletchingWidth = radius * 0.25;
                const fletchingLength = radius * 0.2;
                
                // Arrow position
                const arrowX = x + cellSize / 2;
                const arrowY = y + cellSize / 2;
                
                // Draw arrow shaft
                ctx.fillStyle = '#8B4513'; // Brown shaft
                ctx.beginPath();
                ctx.rect(arrowX - arrowWidth / 2, arrowY - arrowLength / 2, arrowWidth, arrowLength);
                ctx.fill();
                
                // Draw arrowhead
                ctx.fillStyle = '#CCCCCC'; // Silver arrowhead
                ctx.beginPath();
                ctx.moveTo(arrowX, arrowY - arrowLength / 2 - arrowheadLength);
                ctx.lineTo(arrowX + arrowheadWidth / 2, arrowY - arrowLength / 2);
                ctx.lineTo(arrowX - arrowheadWidth / 2, arrowY - arrowLength / 2);
                ctx.closePath();
                ctx.fill();
                
                // Draw fletching
                ctx.fillStyle = '#FF00FF'; // Magenta fletching
                ctx.beginPath();
                ctx.moveTo(arrowX, arrowY + arrowLength / 2 + fletchingLength);
                ctx.lineTo(arrowX + fletchingWidth / 2, arrowY + arrowLength / 2);
                ctx.lineTo(arrowX - fletchingWidth / 2, arrowY + arrowLength / 2);
                ctx.closePath();
                ctx.fill();
            } else if (this.type === 'mage') {
                // Draw a shield for mage power-up
                const shieldWidth = radius * 0.8;
                const shieldHeight = radius * 1.2;
                const shieldX = x + cellSize / 2;
                const shieldY = y + cellSize / 2;
                
                // Draw shield base
                ctx.fillStyle = '#00FFFF'; // Cyan shield
                ctx.beginPath();
                ctx.moveTo(shieldX, shieldY - shieldHeight / 2);
                ctx.lineTo(shieldX + shieldWidth / 2, shieldY - shieldHeight / 3);
                ctx.lineTo(shieldX + shieldWidth / 2, shieldY + shieldHeight / 3);
                ctx.lineTo(shieldX, shieldY + shieldHeight / 2);
                ctx.lineTo(shieldX - shieldWidth / 2, shieldY + shieldHeight / 3);
                ctx.lineTo(shieldX - shieldWidth / 2, shieldY - shieldHeight / 3);
                ctx.closePath();
                ctx.fill();
                
                // Draw shield border
                ctx.strokeStyle = '#0088AA'; // Darker cyan border
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(shieldX, shieldY - shieldHeight / 2);
                ctx.lineTo(shieldX + shieldWidth / 2, shieldY - shieldHeight / 3);
                ctx.lineTo(shieldX + shieldWidth / 2, shieldY + shieldHeight / 3);
                ctx.lineTo(shieldX, shieldY + shieldHeight / 2);
                ctx.lineTo(shieldX - shieldWidth / 2, shieldY + shieldHeight / 3);
                ctx.lineTo(shieldX - shieldWidth / 2, shieldY - shieldHeight / 3);
                ctx.closePath();
                ctx.stroke();
                
                // Draw shield emblem
                ctx.fillStyle = this.team === 1 ? '#0000FF' : '#FF0000'; // Team color
                ctx.beginPath();
                ctx.arc(shieldX, shieldY, radius * 0.25, 0, Math.PI * 2);
                ctx.fill();
            }
        }
    }

    activatePowerUp() {
        // Always activate power-up without any cooldown check
        this.powerUpActive = true;
        
        // Debug log for power-up activation
        console.log(`${this.team === 1 ? 'Blue' : 'Red'} ${this.type} activated power-up permanently!`);
        
        return true;
    }

    updatePowerUp() {
        // No need to update power-up status since they're permanent
        // This method is kept for compatibility but doesn't do anything
    }

    useSpecialAbility(game, direction) {
        if (!this.powerUpActive) return false;
        
        switch(this.type) {
            case 'mage':
                // Create shield in front of the team
                return this.createShield(game, direction);
            case 'archer':
                // Shoot arrows based on collected XP
                return this.shootArrow(game, direction);
            case 'fighter':
                // Break walls or hit enemies
                return this.breakWall(game, direction);
        }
        
        return false;
    }
    
    createShield(game, direction) {
        // Implementation will be in the game logic
        console.log(`${this.team === 1 ? 'Blue' : 'Red'} Mage created a shield!`);
        return true;
    }
    
    shootArrow(game, direction) {
        // Debug log for arrow shooting attempt
        console.log(`${this.team === 1 ? 'Blue' : 'Red'} Archer attempting to shoot arrow:`, {
            arrowCount: this.arrowCount,
            powerUpActive: this.powerUpActive
        });
        
        if (this.arrowCount > 0 && this.powerUpActive) {
            this.arrowCount--;
            console.log(`${this.team === 1 ? 'Blue' : 'Red'} Archer shot an arrow! Remaining: ${this.arrowCount}`);
            return true;
        }
        
        console.log(`${this.team === 1 ? 'Blue' : 'Red'} Archer cannot shoot:`, {
            arrowCount: this.arrowCount,
            powerUpActive: this.powerUpActive
        });
        
        return false;
    }
    
    breakWall(game, direction) {
        // Implementation will be in the game logic
        console.log(`${this.team === 1 ? 'Blue' : 'Red'} Fighter attempted to break a wall!`);
        return true;
    }
    
    collectXp(amount) {
        this.xp += amount;
        if (this.type === 'archer') {
            // Archer gets arrows based on XP
            this.arrowCount = Math.floor(this.xp / 10);
        }
    }
    
    reduceXp(amount) {
        // Reduce XP when hit by an arrow
        this.xp = Math.max(0, this.xp - amount);
        
        // Update archer's arrow count if needed
        if (this.type === 'archer') {
            this.arrowCount = Math.floor(this.xp / 10);
        }
        
        return this.xp;
    }
} 