class Game {
    constructor(canvasId) {
        this.canvas = document.getElementById(canvasId);
        this.ctx = this.canvas.getContext('2d');
        
        // Set canvas size
        this.canvas.width = 800;
        this.canvas.height = 600;
        
        // Game state
        this.state = 'start'; // 'start', 'playing', 'paused', 'gameOver'
        this.winner = null;
        
        // Maze settings
        this.mazeWidth = 31;
        this.mazeHeight = 21;
        this.cellSize = Math.min(
            this.canvas.width / this.mazeWidth,
            this.canvas.height / this.mazeHeight
        );
        
        // Create maze
        this.maze = new Maze(this.mazeWidth, this.mazeHeight);
        
        // Find valid starting positions at opposite sides
        const startPositions = this.findStartPositions();
        
        // Create teams at symmetrical opposite positions
        this.team1 = new Team(1, startPositions.team1.x, startPositions.team1.y);
        this.team2 = new Team(2, startPositions.team2.x, startPositions.team2.y);
        
        // Active effects (shields, arrows, etc.)
        this.activeEffects = [];
        
        // Track wall hits for archer arrows
        this.wallHits = {};
        
        // Input handling
        this.keys = {};
        this.setupInputHandlers();
        
        // UI elements for Team Blue (Team 1)
        this.blueLeaderElement = document.getElementById('blue-leader');
        this.blueTotalXpElement = document.getElementById('blue-total-xp');
        this.blueMageXpElement = document.getElementById('blue-mage-xp');
        this.blueArcherXpElement = document.getElementById('blue-archer-xp');
        this.blueArcherArrowsElement = document.getElementById('blue-archer-arrows');
        this.blueFighterXpElement = document.getElementById('blue-fighter-xp');
        
        // Add power-up status elements for Team Blue
        this.blueMagePowerUpElement = document.getElementById('blue-mage-powerup');
        this.blueArcherPowerUpElement = document.getElementById('blue-archer-powerup');
        this.blueFighterPowerUpElement = document.getElementById('blue-fighter-powerup');
        
        // UI elements for Team Red (Team 2)
        this.redLeaderElement = document.getElementById('red-leader');
        this.redTotalXpElement = document.getElementById('red-total-xp');
        this.redMageXpElement = document.getElementById('red-mage-xp');
        this.redArcherXpElement = document.getElementById('red-archer-xp');
        this.redArcherArrowsElement = document.getElementById('red-archer-arrows');
        this.redFighterXpElement = document.getElementById('red-fighter-xp');
        
        // Add power-up status elements for Team Red
        this.redMagePowerUpElement = document.getElementById('red-mage-powerup');
        this.redArcherPowerUpElement = document.getElementById('red-archer-powerup');
        this.redFighterPowerUpElement = document.getElementById('red-fighter-powerup');
        
        // Character stat containers for highlighting the leader
        this.blueCharacterContainers = {
            'mage': document.getElementById('blue-mage'),
            'archer': document.getElementById('blue-archer'),
            'fighter': document.getElementById('blue-fighter')
        };
        
        this.redCharacterContainers = {
            'mage': document.getElementById('red-mage'),
            'archer': document.getElementById('red-archer'),
            'fighter': document.getElementById('red-fighter')
        };
        
        // Start the game loop
        this.lastTime = 0;
        this.accumulator = 0;
        this.timeStep = 1000 / 60; // 60 FPS
        
        // Start the game
        this.start();
        
        this.centralRoomTimer = null;
        this.centralRoomTimerStart = null;
        this.timerDuration = 10000; // 10 seconds in milliseconds
        this.firstTeamInCentral = null;
        
        // Add arena phase flag
        this.arenaPhaseStarted = false;
        
        // Add arena mode flag and cell size
        this.isArenaMode = false;
        this.arenaCellSize = this.cellSize * 1.5;
    }
    
    findStartPositions() {
        // Find valid starting positions at opposite sides of the maze
        // that are symmetrical and have enough space for the team line
        
        const leftX = 1; // Left side of maze
        const rightX = this.mazeWidth - 2; // Right side of maze
        const middleY = Math.floor(this.mazeHeight / 2);
        
        // Check for valid positions near the middle height
        for (let yOffset = 0; yOffset < 5; yOffset++) {
            // Try positions above and below middle
            const checkYs = [middleY - yOffset, middleY + yOffset];
            
            for (const y of checkYs) {
                // Skip if out of bounds
                if (y < 1 || y >= this.mazeHeight - 1) continue;
                
                // Check if both positions are valid paths
                if (!this.maze.getCell(leftX, y).isWall && !this.maze.getCell(rightX, y).isWall) {
                    // Check if there's space for followers (3 cells in a row)
                    let team1Valid = true;
                    let team2Valid = true;
                    
                    // Check space for team 1 followers (to the left)
                    for (let i = 1; i <= 2; i++) {
                        if (leftX - i < 0 || this.maze.getCell(leftX - i, y).isWall) {
                            team1Valid = false;
                            break;
                        }
                    }
                    
                    // Check space for team 2 followers (to the right)
                    for (let i = 1; i <= 2; i++) {
                        if (rightX + i >= this.mazeWidth || this.maze.getCell(rightX + i, y).isWall) {
                            team2Valid = false;
                            break;
                        }
                    }
                    
                    if (team1Valid && team2Valid) {
                        return {
                            team1: { x: leftX, y: y },
                            team2: { x: rightX, y: y }
                        };
                    }
                }
            }
        }
        
        // Fallback to default positions if no valid positions found
        return {
            team1: { x: 1, y: middleY },
            team2: { x: this.mazeWidth - 2, y: middleY }
        };
    }
    
    start() {
        this.state = 'playing';
        
        // Initialize UI
        this.updateUI();
        
        requestAnimationFrame(this.gameLoop.bind(this));
    }
    
    gameLoop(timestamp) {
        // Calculate delta time
        const deltaTime = timestamp - this.lastTime;
        this.lastTime = timestamp;
        
        // Update game state
        this.accumulator += deltaTime;
        
        while (this.accumulator >= this.timeStep) {
            this.update();
            this.accumulator -= this.timeStep;
        }
        
        // Render the game
        this.render();
        
        // Continue the game loop if not game over
        if (this.state !== 'gameOver') {
            requestAnimationFrame(this.gameLoop.bind(this));
        }
    }
    
    update() {
        if (this.state !== 'playing') return;
        
        // Handle team 1 movement (WASD)
        if (this.keys['KeyW'] && !this.prevKeys['KeyW']) {
            this.moveTeam(this.team1, 'up');
        } else if (this.keys['KeyS'] && !this.prevKeys['KeyS']) {
            this.moveTeam(this.team1, 'down');
        } else if (this.keys['KeyA'] && !this.prevKeys['KeyA']) {
            this.moveTeam(this.team1, 'left');
        } else if (this.keys['KeyD'] && !this.prevKeys['KeyD']) {
            this.moveTeam(this.team1, 'right');
        }
        
        // Handle team 2 movement (Arrow keys)
        if (this.keys['ArrowUp'] && !this.prevKeys['ArrowUp']) {
            this.moveTeam(this.team2, 'up');
        } else if (this.keys['ArrowDown'] && !this.prevKeys['ArrowDown']) {
            this.moveTeam(this.team2, 'down');
        } else if (this.keys['ArrowLeft'] && !this.prevKeys['ArrowLeft']) {
            this.moveTeam(this.team2, 'left');
        } else if (this.keys['ArrowRight'] && !this.prevKeys['ArrowRight']) {
            this.moveTeam(this.team2, 'right');
        }
        
        // Handle team 1 leader change (Q)
        if (this.keys['KeyQ'] && !this.prevKeys['KeyQ']) {
            const newLeader = this.team1.changeLeader();
            this.blueLeaderElement.textContent = newLeader.charAt(0).toUpperCase() + newLeader.slice(1);
            this.updateUI(); // Update UI after leader change
            this.updateLeaderHighlight(this.team1, this.blueCharacterContainers);
        }
        
        // Handle team 2 leader change (P)
        if (this.keys['KeyP'] && !this.prevKeys['KeyP']) {
            const newLeader = this.team2.changeLeader();
            this.redLeaderElement.textContent = newLeader.charAt(0).toUpperCase() + newLeader.slice(1);
            this.updateUI(); // Update UI after leader change
            this.updateLeaderHighlight(this.team2, this.redCharacterContainers);
        }
        
        // Handle team 1 special ability or archer shooting (E)
        if (this.keys['KeyE'] && !this.prevKeys['KeyE']) {
            console.log("Team 1 (E key): Checking if archer can shoot");
            
            // Get the archer from team 1
            const archer = this.team1.characters.find(char => char.type === 'archer');
            
            // Check if archer is leader
            if (this.team1.leader.type === 'archer' && archer) {
                if (this.isArenaMode) {
                    // Enhanced archer abilities in arena
                    if (this.canUseArenaAbility(this.team1, 'archer')) {
                        this.createEnhancedArrow(this.team1);
                        this.showMessage(`🏹 Azul Archer dispara FLECHA DEVASTADORA!`, 2000);
                        this.setArenaAbilityCooldown(this.team1, 'archer', 3000);
                        this.updateUI();
                    } else {
                        this.showMessage(`⏳ Archer en recarga...`, 1000);
                    }
                } else if (archer.powerUpActive && archer.arrowCount > 0) {
                    console.log("Team 1 archer can shoot!");
                    this.shootArrow(this.team1);
                    this.updateUI();
                } else {
                    this.showMessage(`Team 1 Archer needs arrows!`, 1500);
                }
            } else {
                console.log("Team 1 archer cannot shoot, trying other special abilities");
                if (this.useSpecialAbility(this.team1)) {
                    this.updateUI();
                }
            }
        }
        
        // Handle team 2 special ability or archer shooting (Space)
        if (this.keys['Space'] && !this.prevKeys['Space']) {
            console.log("Team 2 (Space key): Checking if archer can shoot");

            // Get the archer from team 2
            const archer = this.team2.characters.find(char => char.type === 'archer');

            // Check if archer is leader
            if (this.team2.leader.type === 'archer' && archer) {
                if (this.isArenaMode) {
                    // Enhanced archer abilities in arena
                    if (this.canUseArenaAbility(this.team2, 'archer')) {
                        this.createEnhancedArrow(this.team2);
                        this.showMessage(`🏹 Rojo Archer dispara FLECHA DEVASTADORA!`, 2000);
                        this.setArenaAbilityCooldown(this.team2, 'archer', 3000);
                        this.updateUI();
                    } else {
                        this.showMessage(`⏳ Archer en recarga...`, 1000);
                    }
                } else if (archer.powerUpActive && archer.arrowCount > 0) {
                    console.log("Team 2 archer can shoot!");
                    this.shootArrow(this.team2);
                    this.updateUI();
                } else {
                    this.showMessage(`Team 2 Archer needs arrows!`, 1500);
                }
            } else {
                console.log("Team 2 archer cannot shoot, trying other special abilities");
                if (this.useSpecialAbility(this.team2)) {
                    this.updateUI();
                }
            }
        }
        
        // Update teams
        this.team1.update();
        this.team2.update();
        
        // Update active effects
        this.updateEffects();
        
        // Check for collisions between teams
        this.checkTeamCollisions();
        
        // Check for win condition
        this.checkWinCondition();
        
        // Update UI
        this.updateUI();
        
        // Store previous key states
        this.prevKeys = {...this.keys};
    }
    
    moveTeam(team, direction) {
        // Calculate new positions for all team members
        const newPositions = this.calculateNewPositions(team, direction);
        const opposingTeam = team === this.team1 ? this.team2 : this.team1;

        // Check if team is trying to leave central room
        if (this.maze.isCentralRoom(team.x, team.y)) {
            const leaderNewPosition = newPositions.find(pos => pos.type === team.leader.type);
            if (leaderNewPosition && !this.maze.isCentralRoom(leaderNewPosition.x, leaderNewPosition.y)) {
                this.showMessage(`Cannot leave the central room once entered!`, 1500);
                return false;
            }
        }

        // Check if any new position would collide with shields
        if (this.checkShieldCollisions(newPositions, team)) {
            return false;
        }

        // Check if leader would move into any opposing team member's position
        // Only check if not in or moving into central room
        const leaderNewPosition = newPositions.find(pos => pos.type === team.leader.type);
        const isMovingToCentralRoom = leaderNewPosition && this.maze.isCentralRoom(leaderNewPosition.x, leaderNewPosition.y);
        const isInCentralRoom = this.maze.isCentralRoom(team.x, team.y);

        if (leaderNewPosition && !isInCentralRoom && !isMovingToCentralRoom) {
            const opposingTeamPositions = opposingTeam.getTeamPositions();
            const leaderCollision = opposingTeamPositions.find(oppPos => 
                oppPos.x === leaderNewPosition.x && 
                oppPos.y === leaderNewPosition.y
            );
            
            if (leaderCollision) {
                const opposingMemberType = leaderCollision.type.charAt(0).toUpperCase() + leaderCollision.type.slice(1);
                this.showMessage(`Team leader cannot move into ${opposingMemberType}'s position!`, 1500);
                return false;
            }
        }

        // If no shield collisions and no leader collision, proceed with normal movement
        if (team.move(direction, this.maze)) {
            // After moving, check if fighter can attack adjacent enemies
            if (team.leader.type === 'fighter') {
                const fighter = team.characters.find(char => char.type === 'fighter');
                if (fighter && fighter.powerUpActive) {
                    // Check for adjacent enemies after movement
                    const adjacentEnemy = this.checkAdjacentEnemy(team, opposingTeam);
                    if (adjacentEnemy) {
                        // Deal damage to the adjacent enemy
                        this.dealDamage(adjacentEnemy.character, opposingTeam, 10);
                        this.knockbackCharacter(opposingTeam, adjacentEnemy.character, direction);
                        
                        const attackerTeamColor = team.teamNumber === 1 ? 'Blue' : 'Red';
                        const targetTeamColor = opposingTeam.teamNumber === 1 ? 'Blue' : 'Red';
                        this.showMessage(`${attackerTeamColor} team's fighter attacked ${targetTeamColor} team's ${adjacentEnemy.character.type}!`, 1500);
                    }
                }
            }
            
            // Handle door, central room, and item collection logic
            return this.handlePostMove(team, direction);
        }
        return false;
    }

    calculateNewPositions(team, direction) {
        const currentPositions = team.getTeamPositions();
        return currentPositions.map(pos => {
            let newX = pos.x;
            let newY = pos.y;
            
            switch(direction) {
                case 'up': newY--; break;
                case 'down': newY++; break;
                case 'left': newX--; break;
                case 'right': newX++; break;
            }
            return { type: pos.type, x: newX, y: newY };
        });
    }

    checkShieldCollisions(newPositions, team) {
        for (const newPos of newPositions) {
            for (const effect of this.activeEffects) {
                if (effect.type === 'shield' && effect.team !== team.teamNumber) {
                    if (newPos.x === effect.x && newPos.y === effect.y) {
                        this.showMessage(`Cannot move through enemy shield!`, 1500);
                        return true;
                    }
                }
            }
        }
        return false;
    }

    detectCollisions(newPositions, opposingTeam) {
        // This method is now only used for other game mechanics, not for preventing movement
        const collisions = [];
        const opposingPositions = opposingTeam.getTeamPositions();

        // Check each new position against all opposing team positions
        for (const newPos of newPositions) {
            for (const oppPos of opposingPositions) {
                if (newPos.x === oppPos.x && newPos.y === oppPos.y) {
                    const enemyChar = opposingTeam.getCharacterAtPosition(oppPos.x, oppPos.y);
                    if (enemyChar) {
                        collisions.push({
                            position: oppPos,
                            character: enemyChar,
                            type: 'direct'
                        });
                    }
                }
            }
        }
        return collisions;
    }

    handlePostMove(team, direction) {
        // Check if the team is at a door and has a key
        const cell = this.maze.getCell(team.x, team.y);
        if (cell.isCentralRoomDoor && !team.hasKey) {
            // Move back if no key
            switch(direction) {
                case 'up': team.move('down', this.maze); break;
                case 'down': team.move('up', this.maze); break;
                case 'left': team.move('right', this.maze); break;
                case 'right': team.move('left', this.maze); break;
            }
            return false;
        }
        
        // Check if team has entered the central room
        if (this.maze.isCentralRoom(team.x, team.y)) {
            const otherTeam = team === this.team1 ? this.team2 : this.team1;
            const bothTeamsInCentral = this.maze.isCentralRoom(otherTeam.x, otherTeam.y);
            
            if (bothTeamsInCentral && !this.arenaPhaseStarted) {
                this.startArenaPhase();
            } else if (!this.firstTeamInCentral) {
                this.startCentralRoomTimer(team);
                const teamColor = team === this.team1 ? 'Blue' : 'Red';
                this.showMessage(`Team ${teamColor} entered the central room first! 10 seconds countdown started.`, 2000);
            }
        }
        
        // Handle power-up collection
        if (cell.hasPowerUp) {
            this.handlePowerUpCollection(team, cell);
        }
        
        // Handle item collection
        const collectedItem = team.collectItem(this.maze);
        if (collectedItem) {
            this.handleItemCollection(team, collectedItem);
        }
        
        // Update UI
        this.updateUI();
        return true;
    }

    handlePowerUpCollection(team, cell) {
        const powerUpType = cell.powerUpType;
        if (this.maze.collectedPowerUps[team.teamNumber].includes(powerUpType)) {
            let powerUpName = '';
            switch(powerUpType) {
                case 'shield': powerUpName = 'Shield'; break;
                case 'arrow': powerUpName = 'Arrow'; break;
                case 'break': powerUpName = 'Break'; break;
            }
            this.showMessage(`Team ${team.teamNumber === 1 ? 'Blue' : 'Red'} already has the ${powerUpName} power-up!`, 2000);
        }
    }

    handleItemCollection(team, collectedItem) {
        if (collectedItem === 'key') {
            console.log(`Team ${team.teamNumber} collected a key!`);
            this.showMessage(`Team ${team.teamNumber === 1 ? 'Blue' : 'Red'} found a key!`, 2000);
        } else if (collectedItem.startsWith('powerUp')) {
            const powerUpType = collectedItem.split('-')[1];
            let powerUpName = '';
            switch(powerUpType) {
                case 'shield': powerUpName = 'Shield'; break;
                case 'arrow': powerUpName = 'Arrow'; break;
                case 'break': powerUpName = 'Break'; break;
            }
            this.showMessage(`Team ${team.teamNumber === 1 ? 'Blue' : 'Red'} activated ${powerUpName} power!`, 2000);
        }
    }
    
    useSpecialAbility(team) {
        const leader = team.leader;
        console.log(`Team ${team.teamNumber} trying to use special ability with leader: ${leader.type}`);

        // Enhanced abilities in arena mode
        if (this.isArenaMode) {
            return this.useArenaSpecialAbility(team, leader);
        }

        // Skip if the leader is an archer (handled separately)
        if (leader.type === 'archer') {
            console.log(`Team ${team.teamNumber} has archer as leader, special abilities handled separately`);
            return false;
        }

        if (leader.powerUpActive) {
            console.log(`Team ${team.teamNumber} ${leader.type} using special ability`);
            switch(leader.type) {
                case 'mage':
                    this.createShield(team);
                    break;
                case 'fighter':
                    this.breakWall(team);
                    break;
            }
            return true;
        }

        console.log(`Team ${team.teamNumber} ${leader.type} has no active power-up`);
        return false;
    }

    useArenaSpecialAbility(team, leader) {
        const teamName = team.teamNumber === 1 ? 'Azul' : 'Rojo';

        // Skip if the leader is an archer (handled separately in arena)
        if (leader.type === 'archer') {
            return false;
        }

        switch(leader.type) {
            case 'mage':
                // Enhanced shield in arena - always available
                this.createEnhancedShield(team);
                this.showMessage(`🛡️ ${teamName} Mage despliega ESCUDO SUPREMO!`, 2000);
                return true;

            case 'fighter':
                // Enhanced attack in arena - area damage
                if (this.canUseArenaAbility(team, 'fighter')) {
                    this.performArenaFighterAttack(team);
                    this.showMessage(`⚔️ ${teamName} Fighter ejecuta ATAQUE DEVASTADOR!`, 2000);
                    this.setArenaAbilityCooldown(team, 'fighter', 4000); // 4 second cooldown
                    return true;
                } else {
                    this.showMessage(`⏳ Fighter en recarga...`, 1000);
                    return false;
                }
        }

        return false;
    }

    canUseArenaAbility(team, abilityType) {
        const now = Date.now();
        const cooldownKey = `${team.teamNumber}_${abilityType}`;

        if (!this.arenaCooldowns) {
            this.arenaCooldowns = {};
        }

        return !this.arenaCooldowns[cooldownKey] || now >= this.arenaCooldowns[cooldownKey];
    }

    setArenaAbilityCooldown(team, abilityType, duration) {
        const cooldownKey = `${team.teamNumber}_${abilityType}`;

        if (!this.arenaCooldowns) {
            this.arenaCooldowns = {};
        }

        this.arenaCooldowns[cooldownKey] = Date.now() + duration;
    }

    createEnhancedShield(team) {
        // Enhanced shield with more durability and larger size
        const shield = {
            type: 'shield',
            team: team.teamNumber,
            x: team.x,
            y: team.y,
            hits: 0,
            maxHits: 5, // Enhanced durability
            size: this.isArenaMode ? this.arenaCellSize * 1.5 : this.cellSize * 1.2,
            enhanced: true
        };

        this.activeEffects.push(shield);
    }

    performArenaFighterAttack(team) {
        const opposingTeam = team === this.team1 ? this.team2 : this.team1;
        const attackRange = 3; // Enhanced range in arena

        // Check for enemies in enhanced range
        const distance = Math.abs(team.x - opposingTeam.x) + Math.abs(team.y - opposingTeam.y);

        if (distance <= attackRange) {
            // Area damage - affects all enemy characters
            opposingTeam.characters.forEach(character => {
                this.dealDamage(character, opposingTeam, 20); // Enhanced damage
            });

            // Knockback effect
            this.knockbackCharacter(opposingTeam, opposingTeam.leader, team.direction);

            // Visual effect
            this.createAreaAttackEffect(team);
        } else {
            this.showMessage(`🎯 No hay enemigos en rango de ataque!`, 1500);
        }
    }

    createAreaAttackEffect(team) {
        // Create visual area attack effect
        const effect = {
            type: 'areaAttack',
            team: team.teamNumber,
            x: team.x,
            y: team.y,
            radius: 0,
            maxRadius: this.isArenaMode ? this.arenaCellSize * 3 : this.cellSize * 2,
            duration: 1000,
            startTime: Date.now()
        };

        this.activeEffects.push(effect);
    }

    createEnhancedArrow(team) {
        // Enhanced arrow with piercing and area damage
        const arrow = {
            type: 'arrow',
            team: team.teamNumber,
            x: team.x,
            y: team.y,
            dx: 0,
            dy: 0,
            distance: 0,
            maxDistance: 15, // Longer range
            isPowered: true,
            enhanced: true,
            piercing: true, // Can hit multiple targets
            areaDamage: true // Damages nearby enemies
        };

        // Set direction based on team direction
        switch(team.direction) {
            case 'up': arrow.dy = -1; break;
            case 'down': arrow.dy = 1; break;
            case 'left': arrow.dx = -1; break;
            case 'right': arrow.dx = 1; break;
        }

        this.activeEffects.push(arrow);
    }

    createShield(team) {
        // Get the mage character
        const mage = team.characters.find(char => char.type === 'mage');
        
        // Create a shield in front of the team
        const direction = team.direction;
        let shieldX = team.x;
        let shieldY = team.y;
        
        switch(direction) {
            case 'up': shieldY--; break;
            case 'down': shieldY++; break;
            case 'left': shieldX--; break;
            case 'right': shieldX++; break;
        }
        
        // Check if the shield position is valid
        if (this.maze.isValidMove(shieldX, shieldY)) {
            // Decrease mage's XP by 10, but ensure it doesn't go below 1
            const newXp = Math.max(1, mage.xp - 10);
            const actualDamage = mage.xp - newXp;
            mage.xp = newXp;
            
            // Show damage message if any damage was taken
            if (actualDamage > 0) {
                console.log(`${team.teamNumber === 1 ? 'Blue' : 'Red'} team's mage used 10 XP to create a shield! XP: ${mage.xp}`);
                this.showMessage(`${team.teamNumber === 1 ? 'Blue' : 'Red'} team's mage used ${actualDamage} XP to create a shield!`, 1500);
            }
            
            this.activeEffects.push({
                type: 'shield',
                team: team.teamNumber,
                x: shieldX,
                y: shieldY,
                direction: direction,
                duration: 300, // 5 seconds at 60fps
                strength: 3 // Can block 3 hits
            });
            
            console.log(`Team ${team.teamNumber} created a shield at (${shieldX}, ${shieldY})!`);
        }
    }
    
    shootArrow(team) {
        console.log(`Shooting arrow for team ${team.teamNumber}`);
        
        // Get the archer
        const archer = team.characters.find(char => char.type === 'archer');
        
        // Reduce arrow count
        archer.arrowCount--;
        
        // Calculate arrow trajectory based on team direction
        let dx = 0, dy = 0;
        switch(team.direction) {
            case 'up': dy = -1; break;
            case 'down': dy = 1; break;
            case 'left': dx = -1; break;
            case 'right': dx = 1; break;
        }
        
        // Add arrow to active effects
        this.activeEffects.push({
            type: 'arrow',
            team: team.teamNumber,
            x: team.x,
            y: team.y,
            dx: dx,
            dy: dy,
            distance: 0,
            maxDistance: 10, // Arrows can travel further with power-up
            isPowered: true // This is a powered arrow that can damage opponents
        });
        
        this.showMessage(`Team ${team.teamNumber === 1 ? 'Blue' : 'Red'} Archer fired an arrow! (${archer.arrowCount} left)`, 1500);
        console.log(`Arrow fired for team ${team.teamNumber} in direction ${team.direction}. Arrows left: ${archer.arrowCount}`);
    }
    
    breakWall(team) {
        const direction = team.direction;
        let targetX = team.x;
        let targetY = team.y;
        
        switch(direction) {
            case 'up': targetY--; break;
            case 'down': targetY++; break;
            case 'left': targetX--; break;
            case 'right': targetX++; break;
        }
        
        // Check if the target is a wall
        const targetCell = this.maze.getCell(targetX, targetY);
        if (targetCell && targetCell.isWall) {
            // Check if the wall is part of the central room
            if (this.maze.isCentralRoomWall(targetX, targetY)) {
                console.log(`Team ${team.teamNumber} cannot break central room walls!`);
                this.showMessage(`Central room walls cannot be broken!`, 1500);
                return;
            }
            
            // Break only the targeted wall, not the symmetrical one
            targetCell.isWall = false;
            
            console.log(`Team ${team.teamNumber} broke a wall at (${targetX}, ${targetY})!`);
            this.showMessage(`Team ${team.teamNumber === 1 ? 'Blue' : 'Red'} broke a wall!`, 1500);
        } else if (targetCell && !targetCell.isWall) {
            // Check if there's an enemy team member at the target position
            const enemyTeam = team.teamNumber === 1 ? this.team2 : this.team1;
            const enemyChar = enemyTeam.getCharacterAtPosition(targetX, targetY);
            
            if (enemyChar) {
                // Get the fighter from the attacking team
                const fighter = team.characters.find(char => char.type === 'fighter');
                
                // Check if the fighter is the leader and has power-up active
                if (fighter && fighter.powerUpActive && team.leader.type === 'fighter') {
                    // Deal damage to the enemy character
                    this.dealDamage(enemyChar, enemyTeam, 10);
                    this.knockbackCharacter(enemyTeam, enemyChar, direction);
                    
                    const attackerTeamColor = team.teamNumber === 1 ? 'Blue' : 'Red';
                    const targetTeamColor = enemyTeam.teamNumber === 1 ? 'Blue' : 'Red';
                    console.log(`${attackerTeamColor} team's fighter attacked ${targetTeamColor} team's ${enemyChar.type}!`);
                    this.showMessage(`${attackerTeamColor} team's fighter attacked ${targetTeamColor} team's ${enemyChar.type}!`, 1500);
                } else if (fighter && !fighter.powerUpActive) {
                    // If fighter doesn't have power-up, show message
                    this.showMessage(`Fighter needs power-up to attack!`, 1500);
                } else if (fighter && team.leader.type !== 'fighter') {
                    // If fighter is not the leader, show message
                    this.showMessage(`Fighter must be the leader to attack!`, 1500);
                }
            }
        }
    }
    
    updateEffects() {
        for (let i = this.activeEffects.length - 1; i >= 0; i--) {
            const effect = this.activeEffects[i];
            
            switch(effect.type) {
                case 'shield':
                    // Update shield duration (enhanced shields last longer)
                    if (effect.enhanced) {
                        // Enhanced shields don't decay over time, only by hits
                        if (effect.hits >= effect.maxHits) {
                            this.activeEffects.splice(i, 1);
                            this.showMessage(`🛡️ Escudo Supremo destruido!`, 1500);
                        }
                    } else {
                        // Regular shields
                        effect.duration--;
                        if (effect.duration <= 0 || effect.strength <= 0) {
                            this.activeEffects.splice(i, 1);
                        }
                    }
                    break;
                    
                case 'arrow':
                    // Move arrow
                    effect.x += effect.dx;
                    effect.y += effect.dy;
                    effect.distance++;

                    // Enhanced arrows in arena mode ignore walls
                    if (!this.isArenaMode) {
                        // Check if arrow hit a wall (only in maze mode)
                        const cell = this.maze.getCell(effect.x, effect.y);
                        if (!cell) {
                            this.activeEffects.splice(i, 1);
                            continue;
                        }

                        if (cell.isWall) {
                            // Handle wall hits for powered arrows
                            if (effect.isPowered) {
                                const wallKey = `${effect.x},${effect.y}`;
                                if (!this.wallHits[wallKey]) {
                                    this.wallHits[wallKey] = 0;
                                }

                                this.wallHits[wallKey]++;

                                // If wall has been hit 3 times, break it
                                if (this.wallHits[wallKey] >= 3) {
                                    cell.isWall = false;
                                    delete this.wallHits[wallKey];
                                    this.showMessage("Wall broken by arrows!", 1500);
                                } else {
                                    this.showMessage(`Wall hit! (${this.wallHits[wallKey]}/3)`, 1000);
                                }
                            }

                            // Regular arrows stop at walls, enhanced arrows continue
                            if (!effect.enhanced) {
                                this.activeEffects.splice(i, 1);
                                continue;
                            }
                        }
                    }
                    
                    // Check if arrow hit a team member
                    const opposingTeam = effect.team === 1 ? this.team2 : this.team1;
                    const hitCharacter = opposingTeam.getCharacterAtPosition(effect.x, effect.y);

                    if (hitCharacter) {
                        // Enhanced arrows deal more damage and have area effect
                        if (effect.enhanced && effect.areaDamage) {
                            // Area damage affects all enemy characters
                            opposingTeam.characters.forEach(character => {
                                this.dealDamage(character, opposingTeam, 15);
                            });
                            this.showMessage(`🏹 FLECHA DEVASTADORA impacta con efecto de área!`, 2000);
                        } else {
                            // Regular arrow damage
                            this.dealDamage(hitCharacter, opposingTeam, 10);
                        }

                        // Piercing arrows continue, regular arrows stop
                        if (!effect.piercing) {
                            this.activeEffects.splice(i, 1);
                            continue;
                        }
                    }
                    
                    // Check if arrow hit a shield
                    for (let j = 0; j < this.activeEffects.length; j++) {
                        const shield = this.activeEffects[j];
                        if (shield.type === 'shield' && shield.team !== effect.team &&
                            shield.x === effect.x && shield.y === effect.y) {
                            // Arrow hit shield
                            console.log(`Team ${effect.team}'s arrow hit Team ${shield.team}'s shield! (${shield.strength - 1} hits remaining)`);
                            
                            // Reduce shield strength
                            shield.strength--;
                            
                            // Show appropriate message based on shield status
                            if (shield.strength > 0) {
                                this.showMessage(`Shield hit! (${shield.strength} hits remaining)`, 1500);
                            } else {
                                // Remove the shield when strength reaches zero
                                this.activeEffects.splice(j, 1);
                                this.showMessage(`Team ${shield.team === 1 ? 'Blue' : 'Red'}'s shield was destroyed!`, 1500);
                                console.log(`Team ${shield.team}'s shield was destroyed! Game continues...`);
                            }
                            
                            // Remove the arrow
                            this.activeEffects.splice(i, 1);
                            break;
                        }
                    }
                    
                    // Check if arrow reached max distance
                    if (effect.distance >= effect.maxDistance) {
                        this.activeEffects.splice(i, 1);
                    }

                    break;

                case 'areaAttack':
                    // Update area attack effect
                    const elapsed = Date.now() - effect.startTime;
                    if (elapsed >= effect.duration) {
                        this.activeEffects.splice(i, 1);
                    }
                    break;
            }
        }
    }
    
    checkTeamCollisions() {
        // Get positions of all team members
        const team1Members = this.team1.getTeamPositions();
        const team2Members = this.team2.getTeamPositions();

        // Check if any team has been eliminated
        this.checkTeamElimination();
    }

    dealDamage(character, team, amount) {
        // Apply arena combat modifiers
        let finalDamage = amount;

        if (this.isArenaMode) {
            // Enhanced damage in arena mode
            finalDamage = Math.floor(amount * 1.5);

            // Show dramatic damage effect
            this.showDamageEffect(character, finalDamage);
        }

        character.xp -= finalDamage;

        // Show damage message with enhanced effects in arena
        if (this.isArenaMode) {
            this.showMessage(`💥 ${finalDamage} daño infligido!`, 1500);
        }

        if (character.xp <= 0) {
            // Enhanced elimination message in arena
            if (this.isArenaMode) {
                const characterName = character.type.charAt(0).toUpperCase() + character.type.slice(1);
                this.showMessage(`⚰️ ${characterName} ha sido ELIMINADO de la arena!`, 2500);
            }

            // Remove character if XP reaches 0
            team.removeCharacter(character.type);
        }
    }

    showDamageEffect(character, damage) {
        // Create floating damage number effect
        const damageEffect = document.createElement('div');
        damageEffect.style.position = 'absolute';
        damageEffect.style.color = '#FF0000';
        damageEffect.style.fontSize = '24px';
        damageEffect.style.fontWeight = 'bold';
        damageEffect.style.textShadow = '0 0 10px #FF0000';
        damageEffect.style.zIndex = '999';
        damageEffect.style.pointerEvents = 'none';
        damageEffect.textContent = `-${damage}`;

        // Position near the character (approximate)
        damageEffect.style.left = '50%';
        damageEffect.style.top = '50%';
        damageEffect.style.transform = 'translate(-50%, -50%)';

        // Add floating animation
        damageEffect.style.animation = 'floatDamage 2s ease-out forwards';

        // Add animation style if not exists
        if (!document.getElementById('damage-animation-style')) {
            const style = document.createElement('style');
            style.id = 'damage-animation-style';
            style.textContent = `
                @keyframes floatDamage {
                    0% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
                    50% { transform: translate(-50%, -100px) scale(1.2); opacity: 1; }
                    100% { transform: translate(-50%, -150px) scale(0.8); opacity: 0; }
                }
            `;
            document.head.appendChild(style);
        }

        document.getElementById('game-container').appendChild(damageEffect);

        // Remove effect after animation
        setTimeout(() => {
            if (damageEffect.parentNode) {
                damageEffect.parentNode.removeChild(damageEffect);
            }
        }, 2000);
    }

    knockbackCharacter(team, character, direction) {
        // Try to move character one space in opposite direction
        // If blocked by shield, try alternate directions
        // If can't move character, try to move whole team
    }

    checkTeamElimination() {
        if (this.team1.characters.length === 0) {
            // Team 2 wins by elimination
            this.state = 'gameOver';
            this.winner = 2;
            this.showMessage('Team Red wins by eliminating all Blue team members!', 5000);
        } else if (this.team2.characters.length === 0) {
            // Team 1 wins by elimination
            this.state = 'gameOver';
            this.winner = 1;
            this.showMessage('Team Blue wins by eliminating all Red team members!', 5000);
        }
    }
    
    checkWinCondition() {
        // Check for team elimination
        if (this.team1.characters.length === 0) {
            this.state = 'gameOver';
            this.winner = 2;
            if (this.isArenaMode) {
                this.showEpicVictory(2);
                this.clearArenaTimer();
            } else {
                this.showMessage('Team Red wins by eliminating all Blue team members!', 5000);
            }
        } else if (this.team2.characters.length === 0) {
            this.state = 'gameOver';
            this.winner = 1;
            if (this.isArenaMode) {
                this.showEpicVictory(1);
                this.clearArenaTimer();
            } else {
                this.showMessage('Team Blue wins by eliminating all Red team members!', 5000);
            }
        }

        // Update arena HUD if in arena mode
        if (this.isArenaMode) {
            this.updateArenaHUD();
        }
    }

    showEpicVictory(winnerTeam) {
        // Clear any existing arena HUD
        const existingHUD = document.getElementById('arena-hud');
        if (existingHUD) {
            existingHUD.remove();
        }

        // Create epic victory screen
        const victoryScreen = document.createElement('div');
        victoryScreen.style.position = 'absolute';
        victoryScreen.style.top = '0';
        victoryScreen.style.left = '0';
        victoryScreen.style.width = '100%';
        victoryScreen.style.height = '100%';
        victoryScreen.style.backgroundColor = 'rgba(0, 0, 0, 0.9)';
        victoryScreen.style.display = 'flex';
        victoryScreen.style.flexDirection = 'column';
        victoryScreen.style.justifyContent = 'center';
        victoryScreen.style.alignItems = 'center';
        victoryScreen.style.zIndex = '1000';
        victoryScreen.style.color = '#fff';
        victoryScreen.style.fontFamily = 'Arial, sans-serif';
        victoryScreen.style.textAlign = 'center';

        const teamName = winnerTeam === 1 ? 'AZUL' : 'ROJO';
        const teamColor = winnerTeam === 1 ? '#0088FF' : '#FF4444';
        const teamEmoji = winnerTeam === 1 ? '🔵' : '🔴';

        victoryScreen.innerHTML = `
            <div style="font-size: 64px; color: ${teamColor}; text-shadow: 0 0 30px ${teamColor}; margin-bottom: 30px; animation: victoryPulse 2s infinite;">
                🏆 ¡VICTORIA! 🏆
            </div>

            <div style="font-size: 48px; color: ${teamColor}; text-shadow: 0 0 20px ${teamColor}; margin-bottom: 20px;">
                ${teamEmoji} EQUIPO ${teamName} ${teamEmoji}
            </div>

            <div style="font-size: 32px; color: #FFD700; text-shadow: 0 0 15px #FFD700; margin-bottom: 30px;">
                ¡CAMPEONES DE LA ARENA SUPREMA!
            </div>

            <div style="font-size: 24px; color: #FFF; margin-bottom: 20px; max-width: 600px;">
                Ha demostrado su supremacía eliminando completamente al equipo contrario en el campo de batalla más desafiante.
            </div>

            <div style="font-size: 20px; color: #FFD700; margin-bottom: 30px; font-style: italic;">
                "La gloria eterna pertenece a los verdaderos guerreros"
            </div>

            <div style="border: 2px solid #FFD700; border-radius: 10px; padding: 20px; background: rgba(255, 215, 0, 0.1); margin: 20px;">
                <div style="font-size: 18px; color: #FFD700; margin-bottom: 15px;">📊 ESTADÍSTICAS FINALES</div>
                <div style="display: flex; justify-content: space-around; gap: 40px;">
                    <div>
                        <div style="color: #0088FF; font-size: 16px;">🔵 EQUIPO AZUL</div>
                        <div>Puntuación: ${this.team1.score}</div>
                        <div>Supervivientes: ${this.team1.characters.length}/3</div>
                    </div>
                    <div>
                        <div style="color: #FF4444; font-size: 16px;">🔴 EQUIPO ROJO</div>
                        <div>Puntuación: ${this.team2.score}</div>
                        <div>Supervivientes: ${this.team2.characters.length}/3</div>
                    </div>
                </div>
            </div>

            <div style="font-size: 16px; color: #AAA; margin-top: 30px;">
                Presiona F5 para jugar otra épica batalla
            </div>
        `;

        // Add victory animation styles
        const style = document.createElement('style');
        style.textContent = `
            @keyframes victoryPulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.1); }
                100% { transform: scale(1); }
            }
        `;
        document.head.appendChild(style);

        document.getElementById('game-container').appendChild(victoryScreen);
    }

    clearArenaTimer() {
        if (this.arenaTimerInterval) {
            clearInterval(this.arenaTimerInterval);
            this.arenaTimerInterval = null;
        }
    }
    
    updateUI() {
        // Update Team Blue (Team 1) UI
        this.blueTotalXpElement.textContent = this.team1.score;
        if (this.team1.characters.length > 0) {
            this.blueLeaderElement.textContent = this.team1.leader.type.charAt(0).toUpperCase() + this.team1.leader.type.slice(1);
        }
        
        // Update individual character XP for Team Blue
        const blueMage = this.team1.characters.find(char => char.type === 'mage');
        if (blueMage) {
            this.blueMageXpElement.textContent = blueMage.xp;
            if (this.blueMagePowerUpElement) {
                this.blueMagePowerUpElement.textContent = blueMage.powerUpActive ? '🛡️' : 'No';
                this.blueMagePowerUpElement.classList.toggle('active', blueMage.powerUpActive);
            }
            this.blueCharacterContainers['mage'].style.display = 'block';
        } else {
            this.blueCharacterContainers['mage'].style.display = 'none';
        }
        
        const blueArcher = this.team1.characters.find(char => char.type === 'archer');
        if (blueArcher) {
            this.blueArcherXpElement.textContent = blueArcher.xp;
            this.blueArcherArrowsElement.textContent = blueArcher.arrowCount;
            if (this.blueArcherPowerUpElement) {
                this.blueArcherPowerUpElement.textContent = blueArcher.powerUpActive ? '🏹' : 'No';
                this.blueArcherPowerUpElement.classList.toggle('active', blueArcher.powerUpActive);
            }
            this.blueCharacterContainers['archer'].style.display = 'block';
        } else {
            this.blueCharacterContainers['archer'].style.display = 'none';
        }
        
        const blueFighter = this.team1.characters.find(char => char.type === 'fighter');
        if (blueFighter) {
            this.blueFighterXpElement.textContent = blueFighter.xp;
            if (this.blueFighterPowerUpElement) {
                this.blueFighterPowerUpElement.textContent = blueFighter.powerUpActive ? '⚔️' : 'No';
                this.blueFighterPowerUpElement.classList.toggle('active', blueFighter.powerUpActive);
            }
            this.blueCharacterContainers['fighter'].style.display = 'block';
        } else {
            this.blueCharacterContainers['fighter'].style.display = 'none';
        }
        
        // Update Team Red (Team 2) UI
        this.redTotalXpElement.textContent = this.team2.score;
        if (this.team2.characters.length > 0) {
            this.redLeaderElement.textContent = this.team2.leader.type.charAt(0).toUpperCase() + this.team2.leader.type.slice(1);
        }
        
        // Update individual character XP for Team Red
        const redMage = this.team2.characters.find(char => char.type === 'mage');
        if (redMage) {
            this.redMageXpElement.textContent = redMage.xp;
            if (this.redMagePowerUpElement) {
                this.redMagePowerUpElement.textContent = redMage.powerUpActive ? '🛡️' : 'No';
                this.redMagePowerUpElement.classList.toggle('active', redMage.powerUpActive);
            }
            this.redCharacterContainers['mage'].style.display = 'block';
        } else {
            this.redCharacterContainers['mage'].style.display = 'none';
        }
        
        const redArcher = this.team2.characters.find(char => char.type === 'archer');
        if (redArcher) {
            this.redArcherXpElement.textContent = redArcher.xp;
            this.redArcherArrowsElement.textContent = redArcher.arrowCount;
            if (this.redArcherPowerUpElement) {
                this.redArcherPowerUpElement.textContent = redArcher.powerUpActive ? '🏹' : 'No';
                this.redArcherPowerUpElement.classList.toggle('active', redArcher.powerUpActive);
            }
            this.redCharacterContainers['archer'].style.display = 'block';
        } else {
            this.redCharacterContainers['archer'].style.display = 'none';
        }
        
        const redFighter = this.team2.characters.find(char => char.type === 'fighter');
        if (redFighter) {
            this.redFighterXpElement.textContent = redFighter.xp;
            if (this.redFighterPowerUpElement) {
                this.redFighterPowerUpElement.textContent = redFighter.powerUpActive ? '⚔️' : 'No';
                this.redFighterPowerUpElement.classList.toggle('active', redFighter.powerUpActive);
            }
            this.redCharacterContainers['fighter'].style.display = 'block';
        } else {
            this.redCharacterContainers['fighter'].style.display = 'none';
        }
        
        // Highlight current leaders
        this.updateLeaderHighlight(this.team1, this.blueCharacterContainers);
        this.updateLeaderHighlight(this.team2, this.redCharacterContainers);
    }
    
    updateLeaderHighlight(team, containers) {
        // Remove leader class from all character containers
        Object.values(containers).forEach(container => {
            container.classList.remove('leader');
        });
        
        // Add leader class to current leader
        containers[team.leader.type].classList.add('leader');
    }
    
    render() {
        // Clear the canvas
        this.ctx.fillStyle = '#000';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        if (this.isArenaMode) {
            this.renderArena();
        } else {
            // Calculate offset to center the maze
            const offsetX = (this.canvas.width - this.maze.width * this.cellSize) / 2;
            const offsetY = (this.canvas.height - this.maze.height * this.cellSize) / 2;
            
            this.ctx.save();
            this.ctx.translate(offsetX, offsetY);
            
            // Render the maze
            this.maze.render(this.ctx, this.cellSize);
            
            // Render active effects
            this.renderEffects();
            
            // Render teams
            this.team1.render(this.ctx, this.cellSize);
            this.team2.render(this.ctx, this.cellSize);
            
            // Render central room timer if active
            if (this.centralRoomTimerStart) {
                const remainingTime = this.getRemainingTime();
                if (remainingTime > 0) {
                    this.ctx.save();
                    this.ctx.fillStyle = '#FFF';
                    this.ctx.font = '24px Arial';
                    this.ctx.textAlign = 'center';
                    const centerX = (this.maze.width * this.cellSize) / 2;
                    const centerY = (this.maze.height * this.cellSize) / 2;
                    this.ctx.fillText(`${Math.ceil(remainingTime / 1000)}s`, centerX, centerY);
                    this.ctx.restore();
                }
            }
            
            this.ctx.restore();
        }
        
        // Render game over screen if needed
        if (this.state === 'gameOver') {
            this.renderGameOver();
        }
    }
    
    renderEffects() {
        for (const effect of this.activeEffects) {
            const cellSize = this.isArenaMode ? this.arenaCellSize : this.cellSize;
            const x = effect.x * cellSize;
            const y = effect.y * cellSize;

            switch(effect.type) {
                case 'shield':
                    // Enhanced shield rendering
                    if (effect.enhanced) {
                        // Pulsating enhanced shield
                        const time = Date.now() * 0.005;
                        const alpha = 0.4 + Math.sin(time) * 0.2;
                        this.ctx.fillStyle = effect.team === 1 ? `rgba(0, 255, 255, ${alpha})` : `rgba(255, 0, 255, ${alpha})`;

                        // Larger enhanced shield
                        const size = effect.size || cellSize;
                        this.ctx.fillRect(x - (size - cellSize) / 2, y - (size - cellSize) / 2, size, size);

                        // Glowing border
                        this.ctx.strokeStyle = effect.team === 1 ? '#00FFFF' : '#FF00FF';
                        this.ctx.lineWidth = 3;
                        this.ctx.strokeRect(x - (size - cellSize) / 2, y - (size - cellSize) / 2, size, size);
                    } else {
                        // Regular shield
                        this.ctx.fillStyle = effect.team === 1 ? 'rgba(0, 255, 255, 0.3)' : 'rgba(255, 0, 255, 0.3)';
                        this.ctx.fillRect(x, y, cellSize, cellSize);
                    }

                    // Draw shield strength
                    this.ctx.fillStyle = '#FFF';
                    this.ctx.font = this.isArenaMode ? '16px Arial' : '12px Arial';
                    const strength = effect.maxHits - effect.hits;
                    this.ctx.fillText(strength.toString(), x + cellSize / 2 - 3, y + cellSize / 2 + 4);
                    break;

                case 'arrow':
                    // Enhanced arrow rendering
                    if (effect.enhanced) {
                        // Glowing enhanced arrow
                        this.ctx.shadowColor = effect.team === 1 ? '#0055FF' : '#FF0000';
                        this.ctx.shadowBlur = 15;
                        this.ctx.fillStyle = effect.team === 1 ? '#00AAFF' : '#FF4444';
                        this.ctx.strokeStyle = '#FFFF00';
                        this.ctx.lineWidth = 3;
                    } else {
                        // Regular arrow
                        this.ctx.fillStyle = effect.team === 1 ? '#0055FF' : '#FF0000';
                        if (effect.isPowered) {
                            this.ctx.strokeStyle = '#FFFF00';
                            this.ctx.lineWidth = 2;
                        }
                    }

                    // Draw arrow based on direction
                    this.ctx.save();
                    this.ctx.translate(x + cellSize / 2, y + cellSize / 2);

                    if (effect.dx > 0) this.ctx.rotate(0);
                    else if (effect.dx < 0) this.ctx.rotate(Math.PI);
                    else if (effect.dy > 0) this.ctx.rotate(Math.PI / 2);
                    else if (effect.dy < 0) this.ctx.rotate(-Math.PI / 2);

                    // Draw arrow shape (larger for enhanced)
                    const arrowSize = effect.enhanced ? cellSize * 0.5 : cellSize * 0.3;
                    this.ctx.beginPath();
                    this.ctx.moveTo(arrowSize, 0);
                    this.ctx.lineTo(-arrowSize * 0.3, -arrowSize * 0.3);
                    this.ctx.lineTo(-arrowSize * 0.3, arrowSize * 0.3);
                    this.ctx.closePath();
                    this.ctx.fill();

                    if (effect.isPowered || effect.enhanced) {
                        this.ctx.stroke();
                    }

                    this.ctx.restore();
                    this.ctx.shadowBlur = 0; // Reset shadow
                    break;

                case 'areaAttack':
                    // Render area attack effect
                    const elapsed = Date.now() - effect.startTime;
                    const progress = Math.min(elapsed / effect.duration, 1);
                    effect.radius = progress * effect.maxRadius;

                    if (progress < 1) {
                        // Expanding circle effect
                        const alpha = 1 - progress;
                        this.ctx.strokeStyle = effect.team === 1 ? `rgba(0, 170, 255, ${alpha})` : `rgba(255, 68, 68, ${alpha})`;
                        this.ctx.lineWidth = 5;
                        this.ctx.beginPath();
                        this.ctx.arc(x + cellSize / 2, y + cellSize / 2, effect.radius, 0, Math.PI * 2);
                        this.ctx.stroke();

                        // Inner explosion effect
                        this.ctx.fillStyle = effect.team === 1 ? `rgba(0, 170, 255, ${alpha * 0.3})` : `rgba(255, 68, 68, ${alpha * 0.3})`;
                        this.ctx.beginPath();
                        this.ctx.arc(x + cellSize / 2, y + cellSize / 2, effect.radius * 0.7, 0, Math.PI * 2);
                        this.ctx.fill();
                    }
                    break;
            }
        }
    }
    
    renderGameOver() {
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        this.ctx.fillRect(0, 0, this.maze.width * this.cellSize, this.maze.height * this.cellSize);
        
        this.ctx.fillStyle = '#FFF';
        this.ctx.font = '30px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(
            `Team ${this.winner} Wins!`,
            this.maze.width * this.cellSize / 2,
            this.maze.height * this.cellSize / 2 - 20
        );
        
        this.ctx.font = '20px Arial';
        this.ctx.fillText(
            `Score: ${this.winner === 1 ? this.team1.score : this.team2.score}`,
            this.maze.width * this.cellSize / 2,
            this.maze.height * this.cellSize / 2 + 20
        );
        
        this.ctx.font = '16px Arial';
        this.ctx.fillText(
            'Refresh the page to play again',
            this.maze.width * this.cellSize / 2,
            this.maze.height * this.cellSize / 2 + 50
        );
    }
    
    setupInputHandlers() {
        // Initialize key states
        this.prevKeys = {};
        
        // Key down handler
        window.addEventListener('keydown', (e) => {
            this.keys[e.code] = true;
            
            // Prevent default for game controls
            if (['KeyW', 'KeyA', 'KeyS', 'KeyD', 'KeyQ', 'KeyE',
                 'ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight',
                 'KeyP', 'Space'].includes(e.code)) {
                e.preventDefault();
            }
        });
        
        // Key up handler
        window.addEventListener('keyup', (e) => {
            this.keys[e.code] = false;
        });
    }
    
    // Add a method to show temporary messages
    showMessage(text, duration) {
        // Create message element if it doesn't exist
        if (!this.messageElement) {
            this.messageElement = document.createElement('div');
            this.messageElement.style.position = 'absolute';
            this.messageElement.style.top = '50%';
            this.messageElement.style.left = '50%';
            this.messageElement.style.transform = 'translate(-50%, -50%)';
            this.messageElement.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
            this.messageElement.style.color = 'white';
            this.messageElement.style.padding = '10px 20px';
            this.messageElement.style.borderRadius = '5px';
            this.messageElement.style.fontSize = '18px';
            this.messageElement.style.textAlign = 'center';
            this.messageElement.style.zIndex = '100';
            this.messageElement.style.pointerEvents = 'none'; // Don't block clicks
            this.messageElement.style.display = 'none';
            
            document.getElementById('game-container').appendChild(this.messageElement);
        }
        
        // Set message text and show it
        this.messageElement.textContent = text;
        this.messageElement.style.display = 'block';
        
        // Clear any existing timeout
        if (this.messageTimeout) {
            clearTimeout(this.messageTimeout);
        }
        
        // Hide message after duration
        this.messageTimeout = setTimeout(() => {
            this.messageElement.style.display = 'none';
        }, duration);
    }

    startCentralRoomTimer(team) {
        if (!this.firstTeamInCentral) {
            this.firstTeamInCentral = team;
            this.centralRoomTimerStart = Date.now();
            this.centralRoomTimer = setTimeout(() => {
                // If the other team hasn't reached the central room, the first team wins
                if (team === this.team1 && !this.maze.isCentralRoom(this.team2.x, this.team2.y)) {
                    this.state = 'gameOver';
                    this.winner = 1;
                    console.log('Team Blue wins - reached central room first!');
                    this.showMessage('Team Blue wins! Red team failed to reach central room in time.', 5000);
                } else if (team === this.team2 && !this.maze.isCentralRoom(this.team1.x, this.team1.y)) {
                    this.state = 'gameOver';
                    this.winner = 2;
                    console.log('Team Red wins - reached central room first!');
                    this.showMessage('Team Red wins! Blue team failed to reach central room in time.', 5000);
                }
            }, this.timerDuration);
        }
    }

    getRemainingTime() {
        if (!this.centralRoomTimerStart) return null;
        const elapsed = Date.now() - this.centralRoomTimerStart;
        return Math.max(0, this.timerDuration - elapsed);
    }

    checkAdjacentEnemy(team, opposingTeam) {
        // Check all adjacent cells for enemies
        const directions = [
            {dx: 0, dy: -1}, // up
            {dx: 0, dy: 1},  // down
            {dx: -1, dy: 0}, // left
            {dx: 1, dy: 0}   // right
        ];

        for (const dir of directions) {
            const checkX = team.x + dir.dx;
            const checkY = team.y + dir.dy;
            
            const enemyChar = opposingTeam.getCharacterAtPosition(checkX, checkY);
            if (enemyChar) {
                return {
                    character: enemyChar,
                    x: checkX,
                    y: checkY
                };
            }
        }
        return null;
    }

    startArenaPhase() {
        this.arenaPhaseStarted = true;

        // Clear any existing central room timer
        if (this.centralRoomTimer) {
            clearTimeout(this.centralRoomTimer);
            this.centralRoomTimer = null;
        }

        // Initialize arena combat timer (3 minutes)
        this.arenaTimeLimit = 180000; // 3 minutes in milliseconds
        this.arenaStartTime = Date.now();

        // Analyze teams before combat
        const team1Analysis = this.analyzeTeamForCombat(this.team1);
        const team2Analysis = this.analyzeTeamForCombat(this.team2);

        // Create the epic arena introduction
        this.createArenaIntroduction(team1Analysis, team2Analysis);

        // Add the AMAZED text with animation
        const amazedText = document.createElement('div');
        amazedText.textContent = 'AMAZED';
        amazedText.style.color = '#FFD700';
        amazedText.style.fontSize = '72px';
        amazedText.style.fontWeight = 'bold';
        amazedText.style.fontFamily = 'Arial, sans-serif';
        amazedText.style.textShadow = '0 0 10px #FFD700';
        amazedText.style.animation = 'amazedAnimation 2s ease-in-out';

    }

    analyzeTeamForCombat(team) {
        const totalPower = team.score;
        const characters = team.characters;

        // Analyze dominant abilities
        const abilities = [];
        characters.forEach(char => {
            if (char.powerUpActive) {
                abilities.push(char.specialAbility);
            }
        });

        // Determine combat specialization
        let specialization = "Equilibrado";
        if (abilities.includes('shield') && abilities.includes('break')) {
            specialization = "Tanque Ofensivo";
        } else if (abilities.includes('shoot') && abilities.includes('shield')) {
            specialization = "Arquero Defensivo";
        } else if (abilities.includes('break') && abilities.includes('shoot')) {
            specialization = "Asalto Rápido";
        } else if (abilities.includes('shield')) {
            specialization = "Defensa Pura";
        } else if (abilities.includes('shoot')) {
            specialization = "Ataque a Distancia";
        } else if (abilities.includes('break')) {
            specialization = "Combate Cuerpo a Cuerpo";
        }

        return {
            totalPower,
            abilities: abilities.slice(0, 3), // Top 3 abilities
            specialization,
            aliveMembers: characters.length
        };
    }

    createArenaIntroduction(team1Analysis, team2Analysis) {
        // Create the epic splash screen
        const splashScreen = document.createElement('div');
        splashScreen.style.position = 'absolute';
        splashScreen.style.top = '0';
        splashScreen.style.left = '0';
        splashScreen.style.width = '100%';
        splashScreen.style.height = '100%';
        splashScreen.style.backgroundColor = 'rgba(0, 0, 0, 0.95)';
        splashScreen.style.display = 'flex';
        splashScreen.style.flexDirection = 'column';
        splashScreen.style.justifyContent = 'center';
        splashScreen.style.alignItems = 'center';
        splashScreen.style.zIndex = '1000';
        splashScreen.style.color = '#fff';
        splashScreen.style.fontFamily = 'Arial, sans-serif';
        splashScreen.style.textAlign = 'center';
        splashScreen.style.overflow = 'auto';

        // Create the epic introduction content
        splashScreen.innerHTML = `
            <div style="max-width: 800px; padding: 20px;">
                <div style="font-size: 48px; color: #FFD700; text-shadow: 0 0 20px #FFD700; margin-bottom: 20px; animation: pulse 2s infinite;">
                    🚨 ¡ATENCIÓN GLADIADORES! 🚨
                </div>

                <div style="font-size: 24px; margin-bottom: 30px; color: #FFF; text-shadow: 0 0 10px #FFF;">
                    Las puertas de la <strong style="color: #FF4444;">ARENA SUPREMA</strong> están a punto de abrirse
                </div>

                <div style="border: 2px solid #FFD700; border-radius: 10px; padding: 20px; margin: 20px 0; background: rgba(255, 215, 0, 0.1);">
                    <div style="font-size: 28px; color: #FFD700; margin-bottom: 15px;">📊 ANÁLISIS PRE-COMBATE</div>

                    <div style="display: flex; justify-content: space-between; margin: 20px 0;">
                        <div style="flex: 1; margin-right: 20px; border: 2px solid #0088FF; border-radius: 8px; padding: 15px; background: rgba(0, 136, 255, 0.1);">
                            <div style="font-size: 20px; color: #0088FF; margin-bottom: 10px;">🔵 EQUIPO AZUL</div>
                            <div style="margin: 5px 0;">💪 <strong>Puntos de Poder:</strong> ${team1Analysis.totalPower}</div>
                            <div style="margin: 5px 0;">⚡ <strong>Habilidades:</strong> ${team1Analysis.abilities.join(', ') || 'Básicas'}</div>
                            <div style="margin: 5px 0;">🎯 <strong>Especialización:</strong> ${team1Analysis.specialization}</div>
                            <div style="margin: 5px 0;">👥 <strong>Miembros:</strong> ${team1Analysis.aliveMembers}/3</div>
                            <div style="color: #00FF00; font-weight: bold; margin-top: 10px;">⚡ PREPARADO PARA EL COMBATE</div>
                        </div>

                        <div style="flex: 1; margin-left: 20px; border: 2px solid #FF4444; border-radius: 8px; padding: 15px; background: rgba(255, 68, 68, 0.1);">
                            <div style="font-size: 20px; color: #FF4444; margin-bottom: 10px;">🔴 EQUIPO ROJO</div>
                            <div style="margin: 5px 0;">💪 <strong>Puntos de Poder:</strong> ${team2Analysis.totalPower}</div>
                            <div style="margin: 5px 0;">⚡ <strong>Habilidades:</strong> ${team2Analysis.abilities.join(', ') || 'Básicas'}</div>
                            <div style="margin: 5px 0;">🎯 <strong>Especialización:</strong> ${team2Analysis.specialization}</div>
                            <div style="margin: 5px 0;">👥 <strong>Miembros:</strong> ${team2Analysis.aliveMembers}/3</div>
                            <div style="color: #00FF00; font-weight: bold; margin-top: 10px;">⚡ PREPARADO PARA EL COMBATE</div>
                        </div>
                    </div>
                </div>

                <div style="border: 2px solid #FF4444; border-radius: 10px; padding: 20px; margin: 20px 0; background: rgba(255, 68, 68, 0.1);">
                    <div style="font-size: 24px; color: #FF4444; margin-bottom: 15px;">⚔️ CONDICIONES DE COMBATE</div>
                    <div style="text-align: left; max-width: 600px; margin: 0 auto;">
                        <div style="margin: 8px 0;">🎯 <strong>Objetivo:</strong> Eliminar a todos los miembros del equipo contrario</div>
                        <div style="margin: 8px 0;">🏟️ <strong>Arena:</strong> Coliseo Tridimensional con obstáculos dinámicos</div>
                        <div style="margin: 8px 0;">⏱️ <strong>Tiempo Límite:</strong> 3 minutos de combate intenso</div>
                        <div style="margin: 8px 0;">🏆 <strong>Victoria por:</strong> Eliminación total o mayor puntuación al final</div>
                    </div>
                </div>

                <div style="border: 2px solid #00FF88; border-radius: 10px; padding: 20px; margin: 20px 0; background: rgba(0, 255, 136, 0.1);">
                    <div style="font-size: 24px; color: #00FF88; margin-bottom: 15px;">🎮 MECÁNICAS ACTIVAS</div>
                    <div style="text-align: left; max-width: 600px; margin: 0 auto;">
                        <div style="margin: 8px 0;">✨ <strong>Habilidades Especiales</strong> desbloqueadas según puntos acumulados</div>
                        <div style="margin: 8px 0;">🛡️ <strong>Sistema de Defensa</strong> proporcional a estadísticas del equipo</div>
                        <div style="margin: 8px 0;">⚡ <strong>Ataques Combinados</strong> disponibles para equipos coordinados</div>
                        <div style="margin: 8px 0;">🔄 <strong>Regeneración</strong> basada en habilidades de supervivencia</div>
                    </div>
                </div>

                <div id="countdown-container" style="font-size: 36px; color: #FFD700; text-shadow: 0 0 20px #FFD700; margin: 30px 0;">
                    <div style="font-size: 28px; margin-bottom: 20px;">🎯 COUNTDOWN FINAL</div>
                    <div id="countdown-number" style="font-size: 72px; font-weight: bold;">3</div>
                    <div id="countdown-text" style="font-size: 20px; margin-top: 10px;">Los equipos toman posiciones en extremos opuestos de la arena</div>
                </div>
            </div>
        `;

        document.body.appendChild(splashScreen);

        // Start the epic countdown
        this.startEpicCountdown(splashScreen);
    }

    startEpicCountdown(splashScreen) {
        const countdownNumber = document.getElementById('countdown-number');
        const countdownText = document.getElementById('countdown-text');

        // Add CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes pulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.1); }
                100% { transform: scale(1); }
            }
            @keyframes countdownPulse {
                0% { transform: scale(1); color: #FFD700; }
                50% { transform: scale(1.5); color: #FF4444; }
                100% { transform: scale(1); color: #FFD700; }
            }
            @keyframes fadeOut {
                0% { opacity: 1; }
                100% { opacity: 0; }
            }
            @keyframes battleStart {
                0% { transform: scale(0.5); opacity: 0; color: #FFD700; }
                50% { transform: scale(1.2); opacity: 1; color: #FF0000; }
                100% { transform: scale(1); opacity: 1; color: #FF4444; }
            }
        `;
        document.head.appendChild(style);

        let count = 3;
        const countdownTexts = [
            "Los equipos toman posiciones en extremos opuestos de la arena",
            "Las habilidades se activan, las armas brillan con poder acumulado",
            "El silencio se apodera del coliseo..."
        ];

        const countdownInterval = setInterval(() => {
            countdownNumber.textContent = count;
            countdownNumber.style.animation = 'countdownPulse 1s ease-in-out';
            countdownText.textContent = countdownTexts[3 - count];

            count--;

            if (count < 0) {
                clearInterval(countdownInterval);

                // Show final battle message
                countdownNumber.textContent = "¡COMBATE!";
                countdownNumber.style.animation = 'battleStart 2s ease-in-out';
                countdownNumber.style.fontSize = '64px';
                countdownText.innerHTML = `
                    <div style="font-size: 32px; color: #FF4444; text-shadow: 0 0 15px #FF4444; margin-top: 20px;">
                        ⚔️ ¡QUE COMIENCE EL COMBATE! ⚔️
                    </div>
                    <div style="font-size: 18px; color: #FFD700; margin-top: 15px; font-style: italic;">
                        "Solo los más fuertes sobrevivirán en la ARENA SUPREMA"
                    </div>
                    <div style="font-size: 16px; color: #00FF88; margin-top: 20px; border: 1px solid #00FF88; padding: 10px; border-radius: 5px;">
                        📱 Usa las habilidades desbloqueadas estratégicamente<br>
                        🤝 Coordina con tu equipo para ataques combinados<br>
                        🏟️ Aprovecha el terreno y los obstáculos de la arena<br>
                        ⚠️ ¡La victoria puede cambiar en cualquier momento!
                    </div>
                    <div style="font-size: 20px; color: #FFD700; margin-top: 15px; font-weight: bold;">
                        🏆 ¡La gloria eterna espera a los vencedores! 🏆
                    </div>
                `;

                // Start arena transformation after final message
                setTimeout(() => {
                    this.startArenaTransformation();

                    // Remove splash screen
                    setTimeout(() => {
                        splashScreen.style.animation = 'fadeOut 1s ease-in-out';
                        setTimeout(() => {
                            document.body.removeChild(splashScreen);
                            this.showArenaHUD();
                        }, 1000);
                    }, 3000);
                }, 2000);
            }
        }, 1500);
    }

    showArenaHUD() {
        // Create arena HUD overlay
        const arenaHUD = document.createElement('div');
        arenaHUD.id = 'arena-hud';
        arenaHUD.style.position = 'absolute';
        arenaHUD.style.top = '10px';
        arenaHUD.style.left = '50%';
        arenaHUD.style.transform = 'translateX(-50%)';
        arenaHUD.style.zIndex = '500';
        arenaHUD.style.color = '#fff';
        arenaHUD.style.fontFamily = 'Arial, sans-serif';
        arenaHUD.style.textAlign = 'center';
        arenaHUD.style.background = 'rgba(0, 0, 0, 0.8)';
        arenaHUD.style.padding = '15px 25px';
        arenaHUD.style.borderRadius = '10px';
        arenaHUD.style.border = '2px solid #FFD700';
        arenaHUD.style.boxShadow = '0 0 20px rgba(255, 215, 0, 0.5)';

        arenaHUD.innerHTML = `
            <div style="font-size: 24px; color: #FFD700; margin-bottom: 10px; text-shadow: 0 0 10px #FFD700;">
                ⚔️ ARENA SUPREMA ⚔️
            </div>
            <div style="display: flex; justify-content: space-between; align-items: center; gap: 30px;">
                <div style="color: #0088FF; font-weight: bold;">
                    🔵 Azul: <span id="arena-blue-alive">${this.team1.characters.length}</span>/3
                </div>
                <div style="color: #FFD700; font-size: 18px; font-weight: bold;">
                    ⏱️ <span id="arena-timer">3:00</span>
                </div>
                <div style="color: #FF4444; font-weight: bold;">
                    🔴 Rojo: <span id="arena-red-alive">${this.team2.characters.length}</span>/3
                </div>
            </div>
            <div style="font-size: 12px; color: #AAA; margin-top: 5px;">
                Victoria por eliminación total o mayor puntuación al final
            </div>
        `;

        document.getElementById('game-container').appendChild(arenaHUD);

        // Start arena timer
        this.startArenaTimer();

        // Show initial combat message
        this.showMessage('¡ARENA ACTIVADA! ¡Elimina al equipo enemigo para ganar!', 3000);
    }

    startArenaTimer() {
        this.arenaTimerInterval = setInterval(() => {
            const elapsed = Date.now() - this.arenaStartTime;
            const remaining = Math.max(0, this.arenaTimeLimit - elapsed);

            const minutes = Math.floor(remaining / 60000);
            const seconds = Math.floor((remaining % 60000) / 1000);

            const timerElement = document.getElementById('arena-timer');
            if (timerElement) {
                timerElement.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;

                // Change color as time runs out
                if (remaining < 30000) { // Last 30 seconds
                    timerElement.style.color = '#FF0000';
                    timerElement.style.animation = 'pulse 1s infinite';
                } else if (remaining < 60000) { // Last minute
                    timerElement.style.color = '#FF8800';
                }
            }

            // Check if time is up
            if (remaining <= 0) {
                clearInterval(this.arenaTimerInterval);
                this.handleArenaTimeUp();
            }
        }, 1000);
    }

    handleArenaTimeUp() {
        // Determine winner by total score
        const team1Score = this.team1.score;
        const team2Score = this.team2.score;

        if (team1Score > team2Score) {
            this.state = 'gameOver';
            this.winner = 1;
            this.showMessage('¡TIEMPO AGOTADO! Team Azul gana por mayor puntuación!', 5000);
        } else if (team2Score > team1Score) {
            this.state = 'gameOver';
            this.winner = 2;
            this.showMessage('¡TIEMPO AGOTADO! Team Rojo gana por mayor puntuación!', 5000);
        } else {
            this.showMessage('¡EMPATE! ¡Muerte súbita activada!', 3000);
            // Continue fighting until elimination
        }
    }

    updateArenaHUD() {
        const blueAliveElement = document.getElementById('arena-blue-alive');
        const redAliveElement = document.getElementById('arena-red-alive');

        if (blueAliveElement) {
            blueAliveElement.textContent = this.team1.characters.length;
        }
        if (redAliveElement) {
            redAliveElement.textContent = this.team2.characters.length;
        }
    }

    startArenaTransformation() {
        // Close all central room doors
        this.maze.closeCentralRoomDoors();

        // Set arena mode
        this.isArenaMode = true;
        
        // Calculate new positions for teams
        const arenaWidth = this.canvas.width;
        const arenaHeight = this.canvas.height;
        
        // Position team 1 on the left side
        this.team1.x = Math.floor(arenaWidth * 0.25 / this.cellSize);
        this.team1.y = Math.floor(arenaHeight * 0.5 / this.cellSize);
        
        // Position team 2 on the right side
        this.team2.x = Math.floor(arenaWidth * 0.75 / this.cellSize);
        this.team2.y = Math.floor(arenaHeight * 0.5 / this.cellSize);
        
        // Adjust cell size for arena mode
        this.arenaCellSize = Math.min(
            this.canvas.width / this.mazeWidth,
            this.canvas.height / this.mazeHeight
        ) * 1.5; // Make cells 50% larger in arena mode
    }

    renderArena() {
        this.ctx.save();

        // Create epic arena floor with multiple gradients
        const centerX = this.canvas.width / 2;
        const centerY = this.canvas.height / 2;

        // Main floor gradient
        const floorGradient = this.ctx.createRadialGradient(
            centerX, centerY, 50,
            centerX, centerY, this.canvas.width / 2
        );
        floorGradient.addColorStop(0, '#3a3a3a');
        floorGradient.addColorStop(0.5, '#2a2a2a');
        floorGradient.addColorStop(1, '#111111');

        // Draw main floor
        this.ctx.fillStyle = floorGradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // Add arena circle pattern
        this.drawArenaPattern();

        // Draw enhanced 3D walls
        this.draw3DWalls();

        // Add dynamic lighting effects
        this.drawDynamicLighting();

        // Add arena obstacles
        this.drawArenaObstacles();

        // Render teams with enhanced 3D effects
        this.renderTeamsIn3D();

        // Render active effects with enhanced 3D appearance
        this.renderEffectsIn3D();

        // Add arena atmosphere effects
        this.drawArenaAtmosphere();

        this.ctx.restore();
    }

    drawArenaPattern() {
        const centerX = this.canvas.width / 2;
        const centerY = this.canvas.height / 2;

        // Draw concentric circles for arena floor
        this.ctx.strokeStyle = 'rgba(255, 215, 0, 0.3)';
        this.ctx.lineWidth = 2;

        for (let i = 1; i <= 5; i++) {
            this.ctx.beginPath();
            this.ctx.arc(centerX, centerY, i * 60, 0, Math.PI * 2);
            this.ctx.stroke();
        }

        // Draw cross lines
        this.ctx.strokeStyle = 'rgba(255, 215, 0, 0.2)';
        this.ctx.lineWidth = 1;
        this.ctx.beginPath();
        this.ctx.moveTo(centerX - 200, centerY);
        this.ctx.lineTo(centerX + 200, centerY);
        this.ctx.moveTo(centerX, centerY - 150);
        this.ctx.lineTo(centerX, centerY + 150);
        this.ctx.stroke();
    }

    drawDynamicLighting() {
        const centerX = this.canvas.width / 2;
        const centerY = this.canvas.height / 2;

        // Pulsating central light
        const time = Date.now() * 0.003;
        const intensity = 0.3 + Math.sin(time) * 0.1;

        const lightGradient = this.ctx.createRadialGradient(
            centerX, centerY, 0,
            centerX, centerY, 200
        );
        lightGradient.addColorStop(0, `rgba(255, 215, 0, ${intensity})`);
        lightGradient.addColorStop(0.5, `rgba(255, 100, 0, ${intensity * 0.5})`);
        lightGradient.addColorStop(1, 'rgba(255, 0, 0, 0)');

        this.ctx.fillStyle = lightGradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
    }

    drawArenaObstacles() {
        // Draw strategic pillars/obstacles
        const obstacles = [
            { x: this.canvas.width * 0.3, y: this.canvas.height * 0.3, size: 30 },
            { x: this.canvas.width * 0.7, y: this.canvas.height * 0.3, size: 30 },
            { x: this.canvas.width * 0.3, y: this.canvas.height * 0.7, size: 30 },
            { x: this.canvas.width * 0.7, y: this.canvas.height * 0.7, size: 30 }
        ];

        obstacles.forEach(obstacle => {
            // Draw 3D pillar effect
            this.ctx.fillStyle = '#444';
            this.ctx.fillRect(obstacle.x - obstacle.size/2, obstacle.y - obstacle.size/2, obstacle.size, obstacle.size);

            // Add 3D top
            this.ctx.fillStyle = '#666';
            this.ctx.beginPath();
            this.ctx.moveTo(obstacle.x - obstacle.size/2, obstacle.y - obstacle.size/2);
            this.ctx.lineTo(obstacle.x - obstacle.size/2 + 10, obstacle.y - obstacle.size/2 - 10);
            this.ctx.lineTo(obstacle.x + obstacle.size/2 + 10, obstacle.y - obstacle.size/2 - 10);
            this.ctx.lineTo(obstacle.x + obstacle.size/2, obstacle.y - obstacle.size/2);
            this.ctx.closePath();
            this.ctx.fill();

            // Add golden trim
            this.ctx.strokeStyle = '#FFD700';
            this.ctx.lineWidth = 2;
            this.ctx.strokeRect(obstacle.x - obstacle.size/2, obstacle.y - obstacle.size/2, obstacle.size, obstacle.size);
        });
    }

    drawArenaAtmosphere() {
        // Add particle effects or atmospheric elements
        const time = Date.now() * 0.001;

        // Floating particles
        for (let i = 0; i < 20; i++) {
            const x = (Math.sin(time + i) * 100) + this.canvas.width / 2;
            const y = (Math.cos(time * 0.7 + i) * 80) + this.canvas.height / 2;

            this.ctx.fillStyle = `rgba(255, 215, 0, ${0.3 + Math.sin(time + i) * 0.2})`;
            this.ctx.beginPath();
            this.ctx.arc(x, y, 2, 0, Math.PI * 2);
            this.ctx.fill();
        }
    }

    draw3DWalls() {
        const wallHeight = 100; // Height of the walls in pixels
        const perspective = 0.3; // Perspective factor
        
        // Draw back wall with perspective
        this.ctx.beginPath();
        this.ctx.fillStyle = '#333333';
        this.ctx.moveTo(10, 10);
        this.ctx.lineTo(this.canvas.width - 10, 10);
        this.ctx.lineTo(this.canvas.width - 10 - wallHeight * perspective, 10 + wallHeight);
        this.ctx.lineTo(10 + wallHeight * perspective, 10 + wallHeight);
        this.ctx.closePath();
        this.ctx.fill();
        
        // Draw right wall with perspective
        this.ctx.beginPath();
        this.ctx.fillStyle = '#2a2a2a';
        this.ctx.moveTo(this.canvas.width - 10, 10);
        this.ctx.lineTo(this.canvas.width - 10, this.canvas.height - 10);
        this.ctx.lineTo(this.canvas.width - 10 - wallHeight * perspective, this.canvas.height - 10 - wallHeight * perspective);
        this.ctx.lineTo(this.canvas.width - 10 - wallHeight * perspective, 10 + wallHeight);
        this.ctx.closePath();
        this.ctx.fill();
        
        // Draw left wall with perspective
        this.ctx.beginPath();
        this.ctx.fillStyle = '#404040';
        this.ctx.moveTo(10, 10);
        this.ctx.lineTo(10, this.canvas.height - 10);
        this.ctx.lineTo(10 + wallHeight * perspective, this.canvas.height - 10 - wallHeight * perspective);
        this.ctx.lineTo(10 + wallHeight * perspective, 10 + wallHeight);
        this.ctx.closePath();
        this.ctx.fill();
        
        // Draw bottom wall with perspective
        this.ctx.beginPath();
        this.ctx.fillStyle = '#262626';
        this.ctx.moveTo(10, this.canvas.height - 10);
        this.ctx.lineTo(this.canvas.width - 10, this.canvas.height - 10);
        this.ctx.lineTo(this.canvas.width - 10 - wallHeight * perspective, this.canvas.height - 10 - wallHeight * perspective);
        this.ctx.lineTo(10 + wallHeight * perspective, this.canvas.height - 10 - wallHeight * perspective);
        this.ctx.closePath();
        this.ctx.fill();
        
        // Add metallic trim to edges
        this.drawMetallicTrim();
    }

    drawMetallicTrim() {
        const trimWidth = 4;
        this.ctx.strokeStyle = '#FFD700';
        this.ctx.lineWidth = trimWidth;
        
        // Draw trim on visible edges
        this.ctx.beginPath();
        // Front frame
        this.ctx.moveTo(10, 10);
        this.ctx.lineTo(10, this.canvas.height - 10);
        this.ctx.lineTo(this.canvas.width - 10, this.canvas.height - 10);
        this.ctx.lineTo(this.canvas.width - 10, 10);
        this.ctx.lineTo(10, 10);
        
        // Back frame
        const perspective = 0.3;
        const wallHeight = 100;
        this.ctx.moveTo(10 + wallHeight * perspective, 10 + wallHeight);
        this.ctx.lineTo(this.canvas.width - 10 - wallHeight * perspective, 10 + wallHeight);
        this.ctx.lineTo(this.canvas.width - 10 - wallHeight * perspective, this.canvas.height - 10 - wallHeight * perspective);
        this.ctx.lineTo(10 + wallHeight * perspective, this.canvas.height - 10 - wallHeight * perspective);
        this.ctx.lineTo(10 + wallHeight * perspective, 10 + wallHeight);
        
        // Connecting lines
        this.ctx.moveTo(10, 10);
        this.ctx.lineTo(10 + wallHeight * perspective, 10 + wallHeight);
        this.ctx.moveTo(this.canvas.width - 10, 10);
        this.ctx.lineTo(this.canvas.width - 10 - wallHeight * perspective, 10 + wallHeight);
        this.ctx.moveTo(10, this.canvas.height - 10);
        this.ctx.lineTo(10 + wallHeight * perspective, this.canvas.height - 10 - wallHeight * perspective);
        this.ctx.moveTo(this.canvas.width - 10, this.canvas.height - 10);
        this.ctx.lineTo(this.canvas.width - 10 - wallHeight * perspective, this.canvas.height - 10 - wallHeight * perspective);
        
        this.ctx.stroke();
    }

    drawAmbientLight() {
        // Create a subtle ambient light effect
        const gradient = this.ctx.createRadialGradient(
            this.canvas.width / 2, this.canvas.height / 2, 100,
            this.canvas.width / 2, this.canvas.height / 2, this.canvas.width
        );
        gradient.addColorStop(0, 'rgba(255, 255, 255, 0.1)');
        gradient.addColorStop(1, 'rgba(0, 0, 0, 0.3)');
        
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
    }

    renderTeamsIn3D() {
        // Save context for shadow settings
        this.ctx.save();
        
        // Set shadow properties for 3D effect
        this.ctx.shadowColor = 'rgba(0, 0, 0, 0.5)';
        this.ctx.shadowBlur = 15;
        this.ctx.shadowOffsetX = 5;
        this.ctx.shadowOffsetY = 5;
        
        // Render teams with enhanced size and shadows
        this.team1.render(this.ctx, this.arenaCellSize);
        this.team2.render(this.ctx, this.arenaCellSize);
        
        this.ctx.restore();
    }

    renderEffectsIn3D() {
        // Save context for effect settings
        this.ctx.save();
        
        // Add glow and shadow effects for active effects
        this.ctx.shadowColor = 'rgba(255, 255, 255, 0.3)';
        this.ctx.shadowBlur = 20;
        this.ctx.shadowOffsetX = 0;
        this.ctx.shadowOffsetY = 0;
        
        this.renderEffects();
        
        this.ctx.restore();
    }
} 