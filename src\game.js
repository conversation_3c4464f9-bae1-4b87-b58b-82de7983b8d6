class Game {
    constructor(canvasId) {
        this.canvas = document.getElementById(canvasId);
        this.ctx = this.canvas.getContext('2d');
        
        // Set canvas size
        this.canvas.width = 800;
        this.canvas.height = 600;
        
        // Game state
        this.state = 'start'; // 'start', 'playing', 'paused', 'gameOver'
        this.winner = null;
        
        // Maze settings
        this.mazeWidth = 31;
        this.mazeHeight = 21;
        this.cellSize = Math.min(
            this.canvas.width / this.mazeWidth,
            this.canvas.height / this.mazeHeight
        );
        
        // Create maze
        this.maze = new Maze(this.mazeWidth, this.mazeHeight);
        
        // Find valid starting positions at opposite sides
        const startPositions = this.findStartPositions();
        
        // Create teams at symmetrical opposite positions
        this.team1 = new Team(1, startPositions.team1.x, startPositions.team1.y);
        this.team2 = new Team(2, startPositions.team2.x, startPositions.team2.y);
        
        // Active effects (shields, arrows, etc.)
        this.activeEffects = [];
        
        // Track wall hits for archer arrows
        this.wallHits = {};
        
        // Input handling
        this.keys = {};
        this.setupInputHandlers();
        
        // UI elements for Team Blue (Team 1)
        this.blueLeaderElement = document.getElementById('blue-leader');
        this.blueTotalXpElement = document.getElementById('blue-total-xp');
        this.blueMageXpElement = document.getElementById('blue-mage-xp');
        this.blueArcherXpElement = document.getElementById('blue-archer-xp');
        this.blueArcherArrowsElement = document.getElementById('blue-archer-arrows');
        this.blueFighterXpElement = document.getElementById('blue-fighter-xp');
        
        // Add power-up status elements for Team Blue
        this.blueMagePowerUpElement = document.getElementById('blue-mage-powerup');
        this.blueArcherPowerUpElement = document.getElementById('blue-archer-powerup');
        this.blueFighterPowerUpElement = document.getElementById('blue-fighter-powerup');
        
        // UI elements for Team Red (Team 2)
        this.redLeaderElement = document.getElementById('red-leader');
        this.redTotalXpElement = document.getElementById('red-total-xp');
        this.redMageXpElement = document.getElementById('red-mage-xp');
        this.redArcherXpElement = document.getElementById('red-archer-xp');
        this.redArcherArrowsElement = document.getElementById('red-archer-arrows');
        this.redFighterXpElement = document.getElementById('red-fighter-xp');
        
        // Add power-up status elements for Team Red
        this.redMagePowerUpElement = document.getElementById('red-mage-powerup');
        this.redArcherPowerUpElement = document.getElementById('red-archer-powerup');
        this.redFighterPowerUpElement = document.getElementById('red-fighter-powerup');
        
        // Character stat containers for highlighting the leader
        this.blueCharacterContainers = {
            'mage': document.getElementById('blue-mage'),
            'archer': document.getElementById('blue-archer'),
            'fighter': document.getElementById('blue-fighter')
        };
        
        this.redCharacterContainers = {
            'mage': document.getElementById('red-mage'),
            'archer': document.getElementById('red-archer'),
            'fighter': document.getElementById('red-fighter')
        };
        
        // Start the game loop
        this.lastTime = 0;
        this.accumulator = 0;
        this.timeStep = 1000 / 60; // 60 FPS
        
        // Start the game
        this.start();
        
        this.centralRoomTimer = null;
        this.centralRoomTimerStart = null;
        this.timerDuration = 10000; // 10 seconds in milliseconds
        this.firstTeamInCentral = null;
        
        // Add arena phase flag
        this.arenaPhaseStarted = false;
        
        // Add arena mode flag and cell size
        this.isArenaMode = false;
        this.arenaCellSize = this.cellSize * 1.5;
    }
    
    findStartPositions() {
        // Find valid starting positions at opposite sides of the maze
        // that are symmetrical and have enough space for the team line
        
        const leftX = 1; // Left side of maze
        const rightX = this.mazeWidth - 2; // Right side of maze
        const middleY = Math.floor(this.mazeHeight / 2);
        
        // Check for valid positions near the middle height
        for (let yOffset = 0; yOffset < 5; yOffset++) {
            // Try positions above and below middle
            const checkYs = [middleY - yOffset, middleY + yOffset];
            
            for (const y of checkYs) {
                // Skip if out of bounds
                if (y < 1 || y >= this.mazeHeight - 1) continue;
                
                // Check if both positions are valid paths
                if (!this.maze.getCell(leftX, y).isWall && !this.maze.getCell(rightX, y).isWall) {
                    // Check if there's space for followers (3 cells in a row)
                    let team1Valid = true;
                    let team2Valid = true;
                    
                    // Check space for team 1 followers (to the left)
                    for (let i = 1; i <= 2; i++) {
                        if (leftX - i < 0 || this.maze.getCell(leftX - i, y).isWall) {
                            team1Valid = false;
                            break;
                        }
                    }
                    
                    // Check space for team 2 followers (to the right)
                    for (let i = 1; i <= 2; i++) {
                        if (rightX + i >= this.mazeWidth || this.maze.getCell(rightX + i, y).isWall) {
                            team2Valid = false;
                            break;
                        }
                    }
                    
                    if (team1Valid && team2Valid) {
                        return {
                            team1: { x: leftX, y: y },
                            team2: { x: rightX, y: y }
                        };
                    }
                }
            }
        }
        
        // Fallback to default positions if no valid positions found
        return {
            team1: { x: 1, y: middleY },
            team2: { x: this.mazeWidth - 2, y: middleY }
        };
    }
    
    start() {
        this.state = 'playing';
        
        // Initialize UI
        this.updateUI();
        
        requestAnimationFrame(this.gameLoop.bind(this));
    }
    
    gameLoop(timestamp) {
        // Calculate delta time
        const deltaTime = timestamp - this.lastTime;
        this.lastTime = timestamp;
        
        // Update game state
        this.accumulator += deltaTime;
        
        while (this.accumulator >= this.timeStep) {
            this.update();
            this.accumulator -= this.timeStep;
        }
        
        // Render the game
        this.render();
        
        // Continue the game loop if not game over
        if (this.state !== 'gameOver') {
            requestAnimationFrame(this.gameLoop.bind(this));
        }
    }
    
    update() {
        if (this.state !== 'playing') return;
        
        // Handle team 1 movement (WASD)
        if (this.keys['KeyW'] && !this.prevKeys['KeyW']) {
            this.moveTeam(this.team1, 'up');
        } else if (this.keys['KeyS'] && !this.prevKeys['KeyS']) {
            this.moveTeam(this.team1, 'down');
        } else if (this.keys['KeyA'] && !this.prevKeys['KeyA']) {
            this.moveTeam(this.team1, 'left');
        } else if (this.keys['KeyD'] && !this.prevKeys['KeyD']) {
            this.moveTeam(this.team1, 'right');
        }
        
        // Handle team 2 movement (Arrow keys)
        if (this.keys['ArrowUp'] && !this.prevKeys['ArrowUp']) {
            this.moveTeam(this.team2, 'up');
        } else if (this.keys['ArrowDown'] && !this.prevKeys['ArrowDown']) {
            this.moveTeam(this.team2, 'down');
        } else if (this.keys['ArrowLeft'] && !this.prevKeys['ArrowLeft']) {
            this.moveTeam(this.team2, 'left');
        } else if (this.keys['ArrowRight'] && !this.prevKeys['ArrowRight']) {
            this.moveTeam(this.team2, 'right');
        }
        
        // Handle team 1 leader change (Q)
        if (this.keys['KeyQ'] && !this.prevKeys['KeyQ']) {
            const newLeader = this.team1.changeLeader();
            this.blueLeaderElement.textContent = newLeader.charAt(0).toUpperCase() + newLeader.slice(1);
            this.updateUI(); // Update UI after leader change
            this.updateLeaderHighlight(this.team1, this.blueCharacterContainers);
        }
        
        // Handle team 2 leader change (P)
        if (this.keys['KeyP'] && !this.prevKeys['KeyP']) {
            const newLeader = this.team2.changeLeader();
            this.redLeaderElement.textContent = newLeader.charAt(0).toUpperCase() + newLeader.slice(1);
            this.updateUI(); // Update UI after leader change
            this.updateLeaderHighlight(this.team2, this.redCharacterContainers);
        }
        
        // Handle team 1 special ability or archer shooting (E)
        if (this.keys['KeyE'] && !this.prevKeys['KeyE']) {
            console.log("Team 1 (E key): Checking if archer can shoot");
            
            // Get the archer from team 1
            const archer = this.team1.characters.find(char => char.type === 'archer');
            
            // Check if archer is leader, has power-up, and has arrows
            if (this.team1.leader.type === 'archer' && archer.powerUpActive && archer.arrowCount > 0) {
                console.log("Team 1 archer can shoot!");
                this.shootArrow(this.team1);
                this.updateUI();
            } else {
                console.log("Team 1 archer cannot shoot, trying other special abilities");
                if (this.useSpecialAbility(this.team1)) {
                    this.updateUI();
                }
            }
        }
        
        // Handle team 2 special ability or archer shooting (Space)
        if (this.keys['Space'] && !this.prevKeys['Space']) {
            console.log("Team 2 (Space key): Checking if archer can shoot");
            
            // Get the archer from team 2
            const archer = this.team2.characters.find(char => char.type === 'archer');
            
            // Check if archer is leader, has power-up, and has arrows
            if (this.team2.leader.type === 'archer' && archer.powerUpActive && archer.arrowCount > 0) {
                console.log("Team 2 archer can shoot!");
                this.shootArrow(this.team2);
                this.updateUI();
            } else {
                console.log("Team 2 archer cannot shoot, trying other special abilities");
                if (this.useSpecialAbility(this.team2)) {
                    this.updateUI();
                }
            }
        }
        
        // Update teams
        this.team1.update();
        this.team2.update();
        
        // Update active effects
        this.updateEffects();
        
        // Check for collisions between teams
        this.checkTeamCollisions();
        
        // Check for win condition
        this.checkWinCondition();
        
        // Update UI
        this.updateUI();
        
        // Store previous key states
        this.prevKeys = {...this.keys};
    }
    
    moveTeam(team, direction) {
        // Calculate new positions for all team members
        const newPositions = this.calculateNewPositions(team, direction);
        const opposingTeam = team === this.team1 ? this.team2 : this.team1;

        // Check if team is trying to leave central room
        if (this.maze.isCentralRoom(team.x, team.y)) {
            const leaderNewPosition = newPositions.find(pos => pos.type === team.leader.type);
            if (leaderNewPosition && !this.maze.isCentralRoom(leaderNewPosition.x, leaderNewPosition.y)) {
                this.showMessage(`Cannot leave the central room once entered!`, 1500);
                return false;
            }
        }

        // Check if any new position would collide with shields
        if (this.checkShieldCollisions(newPositions, team)) {
            return false;
        }

        // Check if leader would move into any opposing team member's position
        // Only check if not in or moving into central room
        const leaderNewPosition = newPositions.find(pos => pos.type === team.leader.type);
        const isMovingToCentralRoom = leaderNewPosition && this.maze.isCentralRoom(leaderNewPosition.x, leaderNewPosition.y);
        const isInCentralRoom = this.maze.isCentralRoom(team.x, team.y);

        if (leaderNewPosition && !isInCentralRoom && !isMovingToCentralRoom) {
            const opposingTeamPositions = opposingTeam.getTeamPositions();
            const leaderCollision = opposingTeamPositions.find(oppPos => 
                oppPos.x === leaderNewPosition.x && 
                oppPos.y === leaderNewPosition.y
            );
            
            if (leaderCollision) {
                const opposingMemberType = leaderCollision.type.charAt(0).toUpperCase() + leaderCollision.type.slice(1);
                this.showMessage(`Team leader cannot move into ${opposingMemberType}'s position!`, 1500);
                return false;
            }
        }

        // If no shield collisions and no leader collision, proceed with normal movement
        if (team.move(direction, this.maze)) {
            // After moving, check if fighter can attack adjacent enemies
            if (team.leader.type === 'fighter') {
                const fighter = team.characters.find(char => char.type === 'fighter');
                if (fighter && fighter.powerUpActive) {
                    // Check for adjacent enemies after movement
                    const adjacentEnemy = this.checkAdjacentEnemy(team, opposingTeam);
                    if (adjacentEnemy) {
                        // Deal damage to the adjacent enemy
                        this.dealDamage(adjacentEnemy.character, opposingTeam, 10);
                        this.knockbackCharacter(opposingTeam, adjacentEnemy.character, direction);
                        
                        const attackerTeamColor = team.teamNumber === 1 ? 'Blue' : 'Red';
                        const targetTeamColor = opposingTeam.teamNumber === 1 ? 'Blue' : 'Red';
                        this.showMessage(`${attackerTeamColor} team's fighter attacked ${targetTeamColor} team's ${adjacentEnemy.character.type}!`, 1500);
                    }
                }
            }
            
            // Handle door, central room, and item collection logic
            return this.handlePostMove(team, direction);
        }
        return false;
    }

    calculateNewPositions(team, direction) {
        const currentPositions = team.getTeamPositions();
        return currentPositions.map(pos => {
            let newX = pos.x;
            let newY = pos.y;
            
            switch(direction) {
                case 'up': newY--; break;
                case 'down': newY++; break;
                case 'left': newX--; break;
                case 'right': newX++; break;
            }
            return { type: pos.type, x: newX, y: newY };
        });
    }

    checkShieldCollisions(newPositions, team) {
        for (const newPos of newPositions) {
            for (const effect of this.activeEffects) {
                if (effect.type === 'shield' && effect.team !== team.teamNumber) {
                    if (newPos.x === effect.x && newPos.y === effect.y) {
                        this.showMessage(`Cannot move through enemy shield!`, 1500);
                        return true;
                    }
                }
            }
        }
        return false;
    }

    detectCollisions(newPositions, opposingTeam) {
        // This method is now only used for other game mechanics, not for preventing movement
        const collisions = [];
        const opposingPositions = opposingTeam.getTeamPositions();

        // Check each new position against all opposing team positions
        for (const newPos of newPositions) {
            for (const oppPos of opposingPositions) {
                if (newPos.x === oppPos.x && newPos.y === oppPos.y) {
                    const enemyChar = opposingTeam.getCharacterAtPosition(oppPos.x, oppPos.y);
                    if (enemyChar) {
                        collisions.push({
                            position: oppPos,
                            character: enemyChar,
                            type: 'direct'
                        });
                    }
                }
            }
        }
        return collisions;
    }

    handlePostMove(team, direction) {
        // Check if the team is at a door and has a key
        const cell = this.maze.getCell(team.x, team.y);
        if (cell.isCentralRoomDoor && !team.hasKey) {
            // Move back if no key
            switch(direction) {
                case 'up': team.move('down', this.maze); break;
                case 'down': team.move('up', this.maze); break;
                case 'left': team.move('right', this.maze); break;
                case 'right': team.move('left', this.maze); break;
            }
            return false;
        }
        
        // Check if team has entered the central room
        if (this.maze.isCentralRoom(team.x, team.y)) {
            const otherTeam = team === this.team1 ? this.team2 : this.team1;
            const bothTeamsInCentral = this.maze.isCentralRoom(otherTeam.x, otherTeam.y);
            
            if (bothTeamsInCentral && !this.arenaPhaseStarted) {
                this.startArenaPhase();
            } else if (!this.firstTeamInCentral) {
                this.startCentralRoomTimer(team);
                const teamColor = team === this.team1 ? 'Blue' : 'Red';
                this.showMessage(`Team ${teamColor} entered the central room first! 10 seconds countdown started.`, 2000);
            }
        }
        
        // Handle power-up collection
        if (cell.hasPowerUp) {
            this.handlePowerUpCollection(team, cell);
        }
        
        // Handle item collection
        const collectedItem = team.collectItem(this.maze);
        if (collectedItem) {
            this.handleItemCollection(team, collectedItem);
        }
        
        // Update UI
        this.updateUI();
        return true;
    }

    handlePowerUpCollection(team, cell) {
        const powerUpType = cell.powerUpType;
        if (this.maze.collectedPowerUps[team.teamNumber].includes(powerUpType)) {
            let powerUpName = '';
            switch(powerUpType) {
                case 'shield': powerUpName = 'Shield'; break;
                case 'arrow': powerUpName = 'Arrow'; break;
                case 'break': powerUpName = 'Break'; break;
            }
            this.showMessage(`Team ${team.teamNumber === 1 ? 'Blue' : 'Red'} already has the ${powerUpName} power-up!`, 2000);
        }
    }

    handleItemCollection(team, collectedItem) {
        if (collectedItem === 'key') {
            console.log(`Team ${team.teamNumber} collected a key!`);
            this.showMessage(`Team ${team.teamNumber === 1 ? 'Blue' : 'Red'} found a key!`, 2000);
        } else if (collectedItem.startsWith('powerUp')) {
            const powerUpType = collectedItem.split('-')[1];
            let powerUpName = '';
            switch(powerUpType) {
                case 'shield': powerUpName = 'Shield'; break;
                case 'arrow': powerUpName = 'Arrow'; break;
                case 'break': powerUpName = 'Break'; break;
            }
            this.showMessage(`Team ${team.teamNumber === 1 ? 'Blue' : 'Red'} activated ${powerUpName} power!`, 2000);
        }
    }
    
    useSpecialAbility(team) {
        const leader = team.leader;
        console.log(`Team ${team.teamNumber} trying to use special ability with leader: ${leader.type}`);
        
        // Skip if the leader is an archer (handled separately)
        if (leader.type === 'archer') {
            console.log(`Team ${team.teamNumber} has archer as leader, special abilities handled separately`);
            return false;
        }
        
        if (leader.powerUpActive) {
            console.log(`Team ${team.teamNumber} ${leader.type} using special ability`);
            switch(leader.type) {
                case 'mage':
                    this.createShield(team);
                    break;
                case 'fighter':
                    this.breakWall(team);
                    break;
            }
            return true;
        }
        
        console.log(`Team ${team.teamNumber} ${leader.type} has no active power-up`);
        return false;
    }
    
    createShield(team) {
        // Get the mage character
        const mage = team.characters.find(char => char.type === 'mage');
        
        // Create a shield in front of the team
        const direction = team.direction;
        let shieldX = team.x;
        let shieldY = team.y;
        
        switch(direction) {
            case 'up': shieldY--; break;
            case 'down': shieldY++; break;
            case 'left': shieldX--; break;
            case 'right': shieldX++; break;
        }
        
        // Check if the shield position is valid
        if (this.maze.isValidMove(shieldX, shieldY)) {
            // Decrease mage's XP by 10, but ensure it doesn't go below 1
            const newXp = Math.max(1, mage.xp - 10);
            const actualDamage = mage.xp - newXp;
            mage.xp = newXp;
            
            // Show damage message if any damage was taken
            if (actualDamage > 0) {
                console.log(`${team.teamNumber === 1 ? 'Blue' : 'Red'} team's mage used 10 XP to create a shield! XP: ${mage.xp}`);
                this.showMessage(`${team.teamNumber === 1 ? 'Blue' : 'Red'} team's mage used ${actualDamage} XP to create a shield!`, 1500);
            }
            
            this.activeEffects.push({
                type: 'shield',
                team: team.teamNumber,
                x: shieldX,
                y: shieldY,
                direction: direction,
                duration: 300, // 5 seconds at 60fps
                strength: 3 // Can block 3 hits
            });
            
            console.log(`Team ${team.teamNumber} created a shield at (${shieldX}, ${shieldY})!`);
        }
    }
    
    shootArrow(team) {
        console.log(`Shooting arrow for team ${team.teamNumber}`);
        
        // Get the archer
        const archer = team.characters.find(char => char.type === 'archer');
        
        // Reduce arrow count
        archer.arrowCount--;
        
        // Calculate arrow trajectory based on team direction
        let dx = 0, dy = 0;
        switch(team.direction) {
            case 'up': dy = -1; break;
            case 'down': dy = 1; break;
            case 'left': dx = -1; break;
            case 'right': dx = 1; break;
        }
        
        // Add arrow to active effects
        this.activeEffects.push({
            type: 'arrow',
            team: team.teamNumber,
            x: team.x,
            y: team.y,
            dx: dx,
            dy: dy,
            distance: 0,
            maxDistance: 10, // Arrows can travel further with power-up
            isPowered: true // This is a powered arrow that can damage opponents
        });
        
        this.showMessage(`Team ${team.teamNumber === 1 ? 'Blue' : 'Red'} Archer fired an arrow! (${archer.arrowCount} left)`, 1500);
        console.log(`Arrow fired for team ${team.teamNumber} in direction ${team.direction}. Arrows left: ${archer.arrowCount}`);
    }
    
    breakWall(team) {
        const direction = team.direction;
        let targetX = team.x;
        let targetY = team.y;
        
        switch(direction) {
            case 'up': targetY--; break;
            case 'down': targetY++; break;
            case 'left': targetX--; break;
            case 'right': targetX++; break;
        }
        
        // Check if the target is a wall
        const targetCell = this.maze.getCell(targetX, targetY);
        if (targetCell && targetCell.isWall) {
            // Check if the wall is part of the central room
            if (this.maze.isCentralRoomWall(targetX, targetY)) {
                console.log(`Team ${team.teamNumber} cannot break central room walls!`);
                this.showMessage(`Central room walls cannot be broken!`, 1500);
                return;
            }
            
            // Break only the targeted wall, not the symmetrical one
            targetCell.isWall = false;
            
            console.log(`Team ${team.teamNumber} broke a wall at (${targetX}, ${targetY})!`);
            this.showMessage(`Team ${team.teamNumber === 1 ? 'Blue' : 'Red'} broke a wall!`, 1500);
        } else if (targetCell && !targetCell.isWall) {
            // Check if there's an enemy team member at the target position
            const enemyTeam = team.teamNumber === 1 ? this.team2 : this.team1;
            const enemyChar = enemyTeam.getCharacterAtPosition(targetX, targetY);
            
            if (enemyChar) {
                // Get the fighter from the attacking team
                const fighter = team.characters.find(char => char.type === 'fighter');
                
                // Check if the fighter is the leader and has power-up active
                if (fighter && fighter.powerUpActive && team.leader.type === 'fighter') {
                    // Deal damage to the enemy character
                    this.dealDamage(enemyChar, enemyTeam, 10);
                    this.knockbackCharacter(enemyTeam, enemyChar, direction);
                    
                    const attackerTeamColor = team.teamNumber === 1 ? 'Blue' : 'Red';
                    const targetTeamColor = enemyTeam.teamNumber === 1 ? 'Blue' : 'Red';
                    console.log(`${attackerTeamColor} team's fighter attacked ${targetTeamColor} team's ${enemyChar.type}!`);
                    this.showMessage(`${attackerTeamColor} team's fighter attacked ${targetTeamColor} team's ${enemyChar.type}!`, 1500);
                } else if (fighter && !fighter.powerUpActive) {
                    // If fighter doesn't have power-up, show message
                    this.showMessage(`Fighter needs power-up to attack!`, 1500);
                } else if (fighter && team.leader.type !== 'fighter') {
                    // If fighter is not the leader, show message
                    this.showMessage(`Fighter must be the leader to attack!`, 1500);
                }
            }
        }
    }
    
    updateEffects() {
        for (let i = this.activeEffects.length - 1; i >= 0; i--) {
            const effect = this.activeEffects[i];
            
            switch(effect.type) {
                case 'shield':
                    // Update shield duration
                    effect.duration--;
                    if (effect.duration <= 0 || effect.strength <= 0) {
                        this.activeEffects.splice(i, 1);
                    }
                    break;
                    
                case 'arrow':
                    // Move arrow
                    effect.x += effect.dx;
                    effect.y += effect.dy;
                    effect.distance++;
                    
                    // Check if arrow hit a wall
                    const cell = this.maze.getCell(effect.x, effect.y);
                    if (!cell) {
                        this.activeEffects.splice(i, 1);
                        continue;
                    }
                    
                    if (cell.isWall) {
                        // Handle wall hits for powered arrows
                        if (effect.isPowered) {
                            const wallKey = `${effect.x},${effect.y}`;
                            if (!this.wallHits[wallKey]) {
                                this.wallHits[wallKey] = 0;
                            }
                            
                            this.wallHits[wallKey]++;
                            
                            // If wall has been hit 3 times, break it
                            if (this.wallHits[wallKey] >= 3) {
                                cell.isWall = false;
                                delete this.wallHits[wallKey];
                                this.showMessage("Wall broken by arrows!", 1500);
                            } else {
                                this.showMessage(`Wall hit! (${this.wallHits[wallKey]}/3)`, 1000);
                            }
                        }
                        
                        this.activeEffects.splice(i, 1);
                        continue;
                    }
                    
                    // Check if arrow hit a team member
                    const opposingTeam = effect.team === 1 ? this.team2 : this.team1;
                    const hitCharacter = opposingTeam.getCharacterAtPosition(effect.x, effect.y);
                    
                    if (hitCharacter) {
                        // Deal damage to the hit character
                        this.dealDamage(hitCharacter, opposingTeam, 10);
                        
                        // Remove the arrow
                        this.activeEffects.splice(i, 1);
                        continue;
                    }
                    
                    // Check if arrow hit a shield
                    for (let j = 0; j < this.activeEffects.length; j++) {
                        const shield = this.activeEffects[j];
                        if (shield.type === 'shield' && shield.team !== effect.team &&
                            shield.x === effect.x && shield.y === effect.y) {
                            // Arrow hit shield
                            console.log(`Team ${effect.team}'s arrow hit Team ${shield.team}'s shield! (${shield.strength - 1} hits remaining)`);
                            
                            // Reduce shield strength
                            shield.strength--;
                            
                            // Show appropriate message based on shield status
                            if (shield.strength > 0) {
                                this.showMessage(`Shield hit! (${shield.strength} hits remaining)`, 1500);
                            } else {
                                // Remove the shield when strength reaches zero
                                this.activeEffects.splice(j, 1);
                                this.showMessage(`Team ${shield.team === 1 ? 'Blue' : 'Red'}'s shield was destroyed!`, 1500);
                                console.log(`Team ${shield.team}'s shield was destroyed! Game continues...`);
                            }
                            
                            // Remove the arrow
                            this.activeEffects.splice(i, 1);
                            break;
                        }
                    }
                    
                    // Check if arrow reached max distance
                    if (effect.distance >= effect.maxDistance) {
                        this.activeEffects.splice(i, 1);
                    }
                    
                    break;
            }
        }
    }
    
    checkTeamCollisions() {
        // Get positions of all team members
        const team1Members = this.team1.getTeamPositions();
        const team2Members = this.team2.getTeamPositions();

        // Check if any team has been eliminated
        this.checkTeamElimination();
    }

    dealDamage(character, team, amount) {
        character.xp -= amount;
        // Show damage message
        if (character.xp <= 0) {
            // Remove character if XP reaches 0
            team.removeCharacter(character.type);
        }
    }

    knockbackCharacter(team, character, direction) {
        // Try to move character one space in opposite direction
        // If blocked by shield, try alternate directions
        // If can't move character, try to move whole team
    }

    checkTeamElimination() {
        if (this.team1.characters.length === 0) {
            // Team 2 wins by elimination
            this.state = 'gameOver';
            this.winner = 2;
            this.showMessage('Team Red wins by eliminating all Blue team members!', 5000);
        } else if (this.team2.characters.length === 0) {
            // Team 1 wins by elimination
            this.state = 'gameOver';
            this.winner = 1;
            this.showMessage('Team Blue wins by eliminating all Red team members!', 5000);
        }
    }
    
    checkWinCondition() {
        // Only check for team elimination
        // Win condition is now only through elimination in the arena phase
        if (this.team1.characters.length === 0) {
            this.state = 'gameOver';
            this.winner = 2;
            this.showMessage('Team Red wins by eliminating all Blue team members!', 5000);
        } else if (this.team2.characters.length === 0) {
            this.state = 'gameOver';
            this.winner = 1;
            this.showMessage('Team Blue wins by eliminating all Red team members!', 5000);
        }
    }
    
    updateUI() {
        // Update Team Blue (Team 1) UI
        this.blueTotalXpElement.textContent = this.team1.score;
        if (this.team1.characters.length > 0) {
            this.blueLeaderElement.textContent = this.team1.leader.type.charAt(0).toUpperCase() + this.team1.leader.type.slice(1);
        }
        
        // Update individual character XP for Team Blue
        const blueMage = this.team1.characters.find(char => char.type === 'mage');
        if (blueMage) {
            this.blueMageXpElement.textContent = blueMage.xp;
            if (this.blueMagePowerUpElement) {
                this.blueMagePowerUpElement.textContent = blueMage.powerUpActive ? '🛡️' : 'No';
                this.blueMagePowerUpElement.classList.toggle('active', blueMage.powerUpActive);
            }
            this.blueCharacterContainers['mage'].style.display = 'block';
        } else {
            this.blueCharacterContainers['mage'].style.display = 'none';
        }
        
        const blueArcher = this.team1.characters.find(char => char.type === 'archer');
        if (blueArcher) {
            this.blueArcherXpElement.textContent = blueArcher.xp;
            this.blueArcherArrowsElement.textContent = blueArcher.arrowCount;
            if (this.blueArcherPowerUpElement) {
                this.blueArcherPowerUpElement.textContent = blueArcher.powerUpActive ? '🏹' : 'No';
                this.blueArcherPowerUpElement.classList.toggle('active', blueArcher.powerUpActive);
            }
            this.blueCharacterContainers['archer'].style.display = 'block';
        } else {
            this.blueCharacterContainers['archer'].style.display = 'none';
        }
        
        const blueFighter = this.team1.characters.find(char => char.type === 'fighter');
        if (blueFighter) {
            this.blueFighterXpElement.textContent = blueFighter.xp;
            if (this.blueFighterPowerUpElement) {
                this.blueFighterPowerUpElement.textContent = blueFighter.powerUpActive ? '⚔️' : 'No';
                this.blueFighterPowerUpElement.classList.toggle('active', blueFighter.powerUpActive);
            }
            this.blueCharacterContainers['fighter'].style.display = 'block';
        } else {
            this.blueCharacterContainers['fighter'].style.display = 'none';
        }
        
        // Update Team Red (Team 2) UI
        this.redTotalXpElement.textContent = this.team2.score;
        if (this.team2.characters.length > 0) {
            this.redLeaderElement.textContent = this.team2.leader.type.charAt(0).toUpperCase() + this.team2.leader.type.slice(1);
        }
        
        // Update individual character XP for Team Red
        const redMage = this.team2.characters.find(char => char.type === 'mage');
        if (redMage) {
            this.redMageXpElement.textContent = redMage.xp;
            if (this.redMagePowerUpElement) {
                this.redMagePowerUpElement.textContent = redMage.powerUpActive ? '🛡️' : 'No';
                this.redMagePowerUpElement.classList.toggle('active', redMage.powerUpActive);
            }
            this.redCharacterContainers['mage'].style.display = 'block';
        } else {
            this.redCharacterContainers['mage'].style.display = 'none';
        }
        
        const redArcher = this.team2.characters.find(char => char.type === 'archer');
        if (redArcher) {
            this.redArcherXpElement.textContent = redArcher.xp;
            this.redArcherArrowsElement.textContent = redArcher.arrowCount;
            if (this.redArcherPowerUpElement) {
                this.redArcherPowerUpElement.textContent = redArcher.powerUpActive ? '🏹' : 'No';
                this.redArcherPowerUpElement.classList.toggle('active', redArcher.powerUpActive);
            }
            this.redCharacterContainers['archer'].style.display = 'block';
        } else {
            this.redCharacterContainers['archer'].style.display = 'none';
        }
        
        const redFighter = this.team2.characters.find(char => char.type === 'fighter');
        if (redFighter) {
            this.redFighterXpElement.textContent = redFighter.xp;
            if (this.redFighterPowerUpElement) {
                this.redFighterPowerUpElement.textContent = redFighter.powerUpActive ? '⚔️' : 'No';
                this.redFighterPowerUpElement.classList.toggle('active', redFighter.powerUpActive);
            }
            this.redCharacterContainers['fighter'].style.display = 'block';
        } else {
            this.redCharacterContainers['fighter'].style.display = 'none';
        }
        
        // Highlight current leaders
        this.updateLeaderHighlight(this.team1, this.blueCharacterContainers);
        this.updateLeaderHighlight(this.team2, this.redCharacterContainers);
    }
    
    updateLeaderHighlight(team, containers) {
        // Remove leader class from all character containers
        Object.values(containers).forEach(container => {
            container.classList.remove('leader');
        });
        
        // Add leader class to current leader
        containers[team.leader.type].classList.add('leader');
    }
    
    render() {
        // Clear the canvas
        this.ctx.fillStyle = '#000';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        if (this.isArenaMode) {
            this.renderArena();
        } else {
            // Calculate offset to center the maze
            const offsetX = (this.canvas.width - this.maze.width * this.cellSize) / 2;
            const offsetY = (this.canvas.height - this.maze.height * this.cellSize) / 2;
            
            this.ctx.save();
            this.ctx.translate(offsetX, offsetY);
            
            // Render the maze
            this.maze.render(this.ctx, this.cellSize);
            
            // Render active effects
            this.renderEffects();
            
            // Render teams
            this.team1.render(this.ctx, this.cellSize);
            this.team2.render(this.ctx, this.cellSize);
            
            // Render central room timer if active
            if (this.centralRoomTimerStart) {
                const remainingTime = this.getRemainingTime();
                if (remainingTime > 0) {
                    this.ctx.save();
                    this.ctx.fillStyle = '#FFF';
                    this.ctx.font = '24px Arial';
                    this.ctx.textAlign = 'center';
                    const centerX = (this.maze.width * this.cellSize) / 2;
                    const centerY = (this.maze.height * this.cellSize) / 2;
                    this.ctx.fillText(`${Math.ceil(remainingTime / 1000)}s`, centerX, centerY);
                    this.ctx.restore();
                }
            }
            
            this.ctx.restore();
        }
        
        // Render game over screen if needed
        if (this.state === 'gameOver') {
            this.renderGameOver();
        }
    }
    
    renderEffects() {
        for (const effect of this.activeEffects) {
            const x = effect.x * this.cellSize;
            const y = effect.y * this.cellSize;
            
            switch(effect.type) {
                case 'shield':
                    // Render shield
                    this.ctx.fillStyle = effect.team === 1 ? 'rgba(0, 255, 255, 0.3)' : 'rgba(255, 0, 255, 0.3)';
                    this.ctx.fillRect(x, y, this.cellSize, this.cellSize);
                    
                    // Draw shield strength
                    this.ctx.fillStyle = '#FFF';
                    this.ctx.font = '12px Arial';
                    this.ctx.fillText(effect.strength.toString(), x + this.cellSize / 2 - 3, y + this.cellSize / 2 + 4);
                    break;
                    
                case 'arrow':
                    // Render arrow
                    this.ctx.fillStyle = effect.team === 1 ? '#0055FF' : '#FF0000';
                    
                    // Make powered arrows more visible
                    if (effect.isPowered) {
                        this.ctx.strokeStyle = '#FFFF00';
                        this.ctx.lineWidth = 2;
                    }
                    
                    // Draw arrow based on direction
                    this.ctx.save();
                    this.ctx.translate(x + this.cellSize / 2, y + this.cellSize / 2);
                    
                    if (effect.dx > 0) this.ctx.rotate(0);
                    else if (effect.dx < 0) this.ctx.rotate(Math.PI);
                    else if (effect.dy > 0) this.ctx.rotate(Math.PI / 2);
                    else if (effect.dy < 0) this.ctx.rotate(-Math.PI / 2);
                    
                    // Draw arrow shape
                    this.ctx.beginPath();
                    this.ctx.moveTo(this.cellSize * 0.3, 0);
                    this.ctx.lineTo(-this.cellSize * 0.1, -this.cellSize * 0.1);
                    this.ctx.lineTo(-this.cellSize * 0.1, this.cellSize * 0.1);
                    this.ctx.closePath();
                    this.ctx.fill();
                    
                    if (effect.isPowered) {
                        this.ctx.stroke();
                    }
                    
                    this.ctx.restore();
                    break;
            }
        }
    }
    
    renderGameOver() {
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        this.ctx.fillRect(0, 0, this.maze.width * this.cellSize, this.maze.height * this.cellSize);
        
        this.ctx.fillStyle = '#FFF';
        this.ctx.font = '30px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(
            `Team ${this.winner} Wins!`,
            this.maze.width * this.cellSize / 2,
            this.maze.height * this.cellSize / 2 - 20
        );
        
        this.ctx.font = '20px Arial';
        this.ctx.fillText(
            `Score: ${this.winner === 1 ? this.team1.score : this.team2.score}`,
            this.maze.width * this.cellSize / 2,
            this.maze.height * this.cellSize / 2 + 20
        );
        
        this.ctx.font = '16px Arial';
        this.ctx.fillText(
            'Refresh the page to play again',
            this.maze.width * this.cellSize / 2,
            this.maze.height * this.cellSize / 2 + 50
        );
    }
    
    setupInputHandlers() {
        // Initialize key states
        this.prevKeys = {};
        
        // Key down handler
        window.addEventListener('keydown', (e) => {
            this.keys[e.code] = true;
            
            // Prevent default for game controls
            if (['KeyW', 'KeyA', 'KeyS', 'KeyD', 'KeyQ', 'KeyE',
                 'ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight',
                 'KeyP', 'Space'].includes(e.code)) {
                e.preventDefault();
            }
        });
        
        // Key up handler
        window.addEventListener('keyup', (e) => {
            this.keys[e.code] = false;
        });
    }
    
    // Add a method to show temporary messages
    showMessage(text, duration) {
        // Create message element if it doesn't exist
        if (!this.messageElement) {
            this.messageElement = document.createElement('div');
            this.messageElement.style.position = 'absolute';
            this.messageElement.style.top = '50%';
            this.messageElement.style.left = '50%';
            this.messageElement.style.transform = 'translate(-50%, -50%)';
            this.messageElement.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
            this.messageElement.style.color = 'white';
            this.messageElement.style.padding = '10px 20px';
            this.messageElement.style.borderRadius = '5px';
            this.messageElement.style.fontSize = '18px';
            this.messageElement.style.textAlign = 'center';
            this.messageElement.style.zIndex = '100';
            this.messageElement.style.pointerEvents = 'none'; // Don't block clicks
            this.messageElement.style.display = 'none';
            
            document.getElementById('game-container').appendChild(this.messageElement);
        }
        
        // Set message text and show it
        this.messageElement.textContent = text;
        this.messageElement.style.display = 'block';
        
        // Clear any existing timeout
        if (this.messageTimeout) {
            clearTimeout(this.messageTimeout);
        }
        
        // Hide message after duration
        this.messageTimeout = setTimeout(() => {
            this.messageElement.style.display = 'none';
        }, duration);
    }

    startCentralRoomTimer(team) {
        if (!this.firstTeamInCentral) {
            this.firstTeamInCentral = team;
            this.centralRoomTimerStart = Date.now();
            this.centralRoomTimer = setTimeout(() => {
                // If the other team hasn't reached the central room, the first team wins
                if (team === this.team1 && !this.maze.isCentralRoom(this.team2.x, this.team2.y)) {
                    this.state = 'gameOver';
                    this.winner = 1;
                    console.log('Team Blue wins - reached central room first!');
                    this.showMessage('Team Blue wins! Red team failed to reach central room in time.', 5000);
                } else if (team === this.team2 && !this.maze.isCentralRoom(this.team1.x, this.team1.y)) {
                    this.state = 'gameOver';
                    this.winner = 2;
                    console.log('Team Red wins - reached central room first!');
                    this.showMessage('Team Red wins! Blue team failed to reach central room in time.', 5000);
                }
            }, this.timerDuration);
        }
    }

    getRemainingTime() {
        if (!this.centralRoomTimerStart) return null;
        const elapsed = Date.now() - this.centralRoomTimerStart;
        return Math.max(0, this.timerDuration - elapsed);
    }

    checkAdjacentEnemy(team, opposingTeam) {
        // Check all adjacent cells for enemies
        const directions = [
            {dx: 0, dy: -1}, // up
            {dx: 0, dy: 1},  // down
            {dx: -1, dy: 0}, // left
            {dx: 1, dy: 0}   // right
        ];

        for (const dir of directions) {
            const checkX = team.x + dir.dx;
            const checkY = team.y + dir.dy;
            
            const enemyChar = opposingTeam.getCharacterAtPosition(checkX, checkY);
            if (enemyChar) {
                return {
                    character: enemyChar,
                    x: checkX,
                    y: checkY
                };
            }
        }
        return null;
    }

    startArenaPhase() {
        this.arenaPhaseStarted = true;
        
        // Clear any existing central room timer
        if (this.centralRoomTimer) {
            clearTimeout(this.centralRoomTimer);
            this.centralRoomTimer = null;
        }

        // Create and show the splash screen
        const splashScreen = document.createElement('div');
        splashScreen.style.position = 'absolute';
        splashScreen.style.top = '0';
        splashScreen.style.left = '0';
        splashScreen.style.width = '100%';
        splashScreen.style.height = '100%';
        splashScreen.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
        splashScreen.style.display = 'flex';
        splashScreen.style.flexDirection = 'column';
        splashScreen.style.justifyContent = 'center';
        splashScreen.style.alignItems = 'center';
        splashScreen.style.zIndex = '1000';

        // Add the AMAZED text with animation
        const amazedText = document.createElement('div');
        amazedText.textContent = 'AMAZED';
        amazedText.style.color = '#FFD700';
        amazedText.style.fontSize = '72px';
        amazedText.style.fontWeight = 'bold';
        amazedText.style.fontFamily = 'Arial, sans-serif';
        amazedText.style.textShadow = '0 0 10px #FFD700';
        amazedText.style.animation = 'amazedAnimation 2s ease-in-out';

        // Add the Arena Phase text
        const arenaText = document.createElement('div');
        arenaText.textContent = 'ARENA PHASE';
        arenaText.style.color = '#FF0000';
        arenaText.style.fontSize = '36px';
        arenaText.style.marginTop = '20px';
        arenaText.style.fontFamily = 'Arial, sans-serif';
        arenaText.style.textShadow = '0 0 5px #FF0000';
        arenaText.style.animation = 'arenaAnimation 2s ease-in-out';

        // Add the battle text
        const battleText = document.createElement('div');
        battleText.textContent = 'Last Team Standing Wins!';
        battleText.style.color = '#FFFFFF';
        battleText.style.fontSize = '24px';
        battleText.style.marginTop = '20px';
        battleText.style.fontFamily = 'Arial, sans-serif';
        battleText.style.textShadow = '0 0 5px #FFFFFF';
        battleText.style.animation = 'arenaAnimation 2s ease-in-out';

        // Add the animation styles
        const style = document.createElement('style');
        style.textContent = `
            @keyframes amazedAnimation {
                0% { transform: scale(0); opacity: 0; }
                50% { transform: scale(1.2); opacity: 1; }
                100% { transform: scale(1); opacity: 1; }
            }
            @keyframes arenaAnimation {
                0% { transform: translateY(50px); opacity: 0; }
                50% { transform: translateY(0); opacity: 0; }
                100% { transform: translateY(0); opacity: 1; }
            }
            @keyframes fadeOut {
                0% { opacity: 1; }
                100% { opacity: 0; }
            }
        `;
        document.head.appendChild(style);

        // Add elements to the splash screen
        splashScreen.appendChild(amazedText);
        splashScreen.appendChild(arenaText);
        splashScreen.appendChild(battleText);
        document.body.appendChild(splashScreen);

        // Start the arena transformation
        this.startArenaTransformation();

        // Remove the splash screen after animation
        setTimeout(() => {
            splashScreen.style.animation = 'fadeOut 1s ease-in-out';
            setTimeout(() => {
                document.body.removeChild(splashScreen);
                this.showMessage('Arena Phase: Eliminate the Enemy Team to Win!', 2000);
            }, 1000);
        }, 3000);
    }

    startArenaTransformation() {
        // Close all central room doors
        this.maze.closeCentralRoomDoors();
        
        // Set arena mode
        this.isArenaMode = true;
        
        // Calculate new positions for teams
        const arenaWidth = this.canvas.width;
        const arenaHeight = this.canvas.height;
        
        // Position team 1 on the left side
        this.team1.x = Math.floor(arenaWidth * 0.25 / this.cellSize);
        this.team1.y = Math.floor(arenaHeight * 0.5 / this.cellSize);
        
        // Position team 2 on the right side
        this.team2.x = Math.floor(arenaWidth * 0.75 / this.cellSize);
        this.team2.y = Math.floor(arenaHeight * 0.5 / this.cellSize);
        
        // Adjust cell size for arena mode
        this.arenaCellSize = Math.min(
            this.canvas.width / this.mazeWidth,
            this.canvas.height / this.mazeHeight
        ) * 1.5; // Make cells 50% larger in arena mode
    }

    renderArena() {
        this.ctx.save();
        
        // Create gradient for the floor to give depth
        const floorGradient = this.ctx.createRadialGradient(
            this.canvas.width / 2, this.canvas.height / 2, 50,
            this.canvas.width / 2, this.canvas.height / 2, this.canvas.width / 2
        );
        floorGradient.addColorStop(0, '#2a2a2a');
        floorGradient.addColorStop(1, '#111111');
        
        // Draw floor with gradient
        this.ctx.fillStyle = floorGradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Draw 3D walls
        this.draw3DWalls();
        
        // Add ambient lighting effect
        this.drawAmbientLight();
        
        // Render teams with larger cell size and shadows
        this.renderTeamsIn3D();
        
        // Render active effects with 3D appearance
        this.renderEffectsIn3D();
        
        this.ctx.restore();
    }

    draw3DWalls() {
        const wallHeight = 100; // Height of the walls in pixels
        const perspective = 0.3; // Perspective factor
        
        // Draw back wall with perspective
        this.ctx.beginPath();
        this.ctx.fillStyle = '#333333';
        this.ctx.moveTo(10, 10);
        this.ctx.lineTo(this.canvas.width - 10, 10);
        this.ctx.lineTo(this.canvas.width - 10 - wallHeight * perspective, 10 + wallHeight);
        this.ctx.lineTo(10 + wallHeight * perspective, 10 + wallHeight);
        this.ctx.closePath();
        this.ctx.fill();
        
        // Draw right wall with perspective
        this.ctx.beginPath();
        this.ctx.fillStyle = '#2a2a2a';
        this.ctx.moveTo(this.canvas.width - 10, 10);
        this.ctx.lineTo(this.canvas.width - 10, this.canvas.height - 10);
        this.ctx.lineTo(this.canvas.width - 10 - wallHeight * perspective, this.canvas.height - 10 - wallHeight * perspective);
        this.ctx.lineTo(this.canvas.width - 10 - wallHeight * perspective, 10 + wallHeight);
        this.ctx.closePath();
        this.ctx.fill();
        
        // Draw left wall with perspective
        this.ctx.beginPath();
        this.ctx.fillStyle = '#404040';
        this.ctx.moveTo(10, 10);
        this.ctx.lineTo(10, this.canvas.height - 10);
        this.ctx.lineTo(10 + wallHeight * perspective, this.canvas.height - 10 - wallHeight * perspective);
        this.ctx.lineTo(10 + wallHeight * perspective, 10 + wallHeight);
        this.ctx.closePath();
        this.ctx.fill();
        
        // Draw bottom wall with perspective
        this.ctx.beginPath();
        this.ctx.fillStyle = '#262626';
        this.ctx.moveTo(10, this.canvas.height - 10);
        this.ctx.lineTo(this.canvas.width - 10, this.canvas.height - 10);
        this.ctx.lineTo(this.canvas.width - 10 - wallHeight * perspective, this.canvas.height - 10 - wallHeight * perspective);
        this.ctx.lineTo(10 + wallHeight * perspective, this.canvas.height - 10 - wallHeight * perspective);
        this.ctx.closePath();
        this.ctx.fill();
        
        // Add metallic trim to edges
        this.drawMetallicTrim();
    }

    drawMetallicTrim() {
        const trimWidth = 4;
        this.ctx.strokeStyle = '#FFD700';
        this.ctx.lineWidth = trimWidth;
        
        // Draw trim on visible edges
        this.ctx.beginPath();
        // Front frame
        this.ctx.moveTo(10, 10);
        this.ctx.lineTo(10, this.canvas.height - 10);
        this.ctx.lineTo(this.canvas.width - 10, this.canvas.height - 10);
        this.ctx.lineTo(this.canvas.width - 10, 10);
        this.ctx.lineTo(10, 10);
        
        // Back frame
        const perspective = 0.3;
        const wallHeight = 100;
        this.ctx.moveTo(10 + wallHeight * perspective, 10 + wallHeight);
        this.ctx.lineTo(this.canvas.width - 10 - wallHeight * perspective, 10 + wallHeight);
        this.ctx.lineTo(this.canvas.width - 10 - wallHeight * perspective, this.canvas.height - 10 - wallHeight * perspective);
        this.ctx.lineTo(10 + wallHeight * perspective, this.canvas.height - 10 - wallHeight * perspective);
        this.ctx.lineTo(10 + wallHeight * perspective, 10 + wallHeight);
        
        // Connecting lines
        this.ctx.moveTo(10, 10);
        this.ctx.lineTo(10 + wallHeight * perspective, 10 + wallHeight);
        this.ctx.moveTo(this.canvas.width - 10, 10);
        this.ctx.lineTo(this.canvas.width - 10 - wallHeight * perspective, 10 + wallHeight);
        this.ctx.moveTo(10, this.canvas.height - 10);
        this.ctx.lineTo(10 + wallHeight * perspective, this.canvas.height - 10 - wallHeight * perspective);
        this.ctx.moveTo(this.canvas.width - 10, this.canvas.height - 10);
        this.ctx.lineTo(this.canvas.width - 10 - wallHeight * perspective, this.canvas.height - 10 - wallHeight * perspective);
        
        this.ctx.stroke();
    }

    drawAmbientLight() {
        // Create a subtle ambient light effect
        const gradient = this.ctx.createRadialGradient(
            this.canvas.width / 2, this.canvas.height / 2, 100,
            this.canvas.width / 2, this.canvas.height / 2, this.canvas.width
        );
        gradient.addColorStop(0, 'rgba(255, 255, 255, 0.1)');
        gradient.addColorStop(1, 'rgba(0, 0, 0, 0.3)');
        
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
    }

    renderTeamsIn3D() {
        // Save context for shadow settings
        this.ctx.save();
        
        // Set shadow properties for 3D effect
        this.ctx.shadowColor = 'rgba(0, 0, 0, 0.5)';
        this.ctx.shadowBlur = 15;
        this.ctx.shadowOffsetX = 5;
        this.ctx.shadowOffsetY = 5;
        
        // Render teams with enhanced size and shadows
        this.team1.render(this.ctx, this.arenaCellSize);
        this.team2.render(this.ctx, this.arenaCellSize);
        
        this.ctx.restore();
    }

    renderEffectsIn3D() {
        // Save context for effect settings
        this.ctx.save();
        
        // Add glow and shadow effects for active effects
        this.ctx.shadowColor = 'rgba(255, 255, 255, 0.3)';
        this.ctx.shadowBlur = 20;
        this.ctx.shadowOffsetX = 0;
        this.ctx.shadowOffsetY = 0;
        
        this.renderEffects();
        
        this.ctx.restore();
    }
} 