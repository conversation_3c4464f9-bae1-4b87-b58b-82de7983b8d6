// Wait for the DOM to be fully loaded
document.addEventListener('DOMContentLoaded', () => {
    // Create and start the game
    const game = new Game('gameCanvas');
    
    // Add event listener for window resize to adjust canvas size
    window.addEventListener('resize', () => {
        // Adjust canvas size based on window size
        const gameContainer = document.getElementById('game-container');
        const maxWidth = window.innerWidth * 0.9;
        const maxHeight = window.innerHeight * 0.8;
        
        // Calculate new dimensions while maintaining aspect ratio
        const aspectRatio = game.canvas.width / game.canvas.height;
        let newWidth = maxWidth;
        let newHeight = newWidth / aspectRatio;
        
        if (newHeight > maxHeight) {
            newHeight = maxHeight;
            newWidth = newHeight * aspectRatio;
        }
        
        // Update canvas style
        game.canvas.style.width = `${newWidth}px`;
        game.canvas.style.height = `${newHeight}px`;
    });
    
    // Trigger resize event to set initial size
    window.dispatchEvent(new Event('resize'));
    
    // Add a simple start screen with instructions
    const createStartScreen = () => {
        const startScreen = document.createElement('div');
        startScreen.id = 'start-screen';
        startScreen.style.position = 'absolute';
        startScreen.style.top = '0';
        startScreen.style.left = '0';
        startScreen.style.width = '100%';
        startScreen.style.height = '100%';
        startScreen.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
        startScreen.style.display = 'flex';
        startScreen.style.flexDirection = 'column';
        startScreen.style.justifyContent = 'center';
        startScreen.style.alignItems = 'center';
        startScreen.style.color = 'white';
        startScreen.style.zIndex = '100';
        
        const title = document.createElement('h1');
        title.textContent = 'Amazed - D&D Pacman';
        title.style.marginBottom = '20px';
        
        const description = document.createElement('div');
        description.innerHTML = `
            <p>Welcome to Amazed, a D&D-inspired Pacman maze game!</p>
            <h2>How to Play:</h2>
            <div style="text-align: left; max-width: 600px; margin: 0 auto;">
                <h3>Team Blue (Left):</h3>
                <ul>
                    <li>Move: Press WASD keys to move one step at a time</li>
                    <li>Change Leader: Q key</li>
                    <li>Use Special Ability: E key</li>
                </ul>
                
                <h3>Team Red (Right):</h3>
                <ul>
                    <li>Move: Press Arrow keys to move one step at a time</li>
                    <li>Change Leader: P key</li>
                    <li>Use Special Ability: Space key</li>
                </ul>
                
                <h3>Characters:</h3>
                <ul>
                    <li><strong>Mage (M):</strong> Creates a defensive shield</li>
                    <li><strong>Archer (A):</strong> Shoots arrows based on collected XP. When the Archer is the leader and has the Arrow power-up, their arrows can:
                        <ul>
                            <li>Reduce opponent's XP when hit</li>
                            <li>Break walls after 3 hits on the same wall</li>
                        </ul>
                    </li>
                    <li><strong>Fighter (F):</strong> Can break walls or hit enemies</li>
                </ul>
                
                <h3>Team Formation:</h3>
                <p>Characters follow the leader in a line. The leader collects XP from pellets.</p>
                
                <h3>Power-ups:</h3>
                <p>Power-ups are placed symmetrically in the maze. Each type has one for Team Blue and one for Team Red.</p>
                <p><strong>Important:</strong> Each team can only collect each type of power-up once! If you already have a Shield power-up, you cannot collect another Shield.</p>
                <p>There are three types of power-ups: Shield (for Mage), Arrow (for Archer), and Break (for Fighter).</p>
                
                <h3>Objective:</h3>
                <p>Collect pellets for XP, find power-ups, get the key to the central room, and be the first team to reach the center!</p>
            </div>
        `;
        
        const startButton = document.createElement('button');
        startButton.textContent = 'Start Game';
        startButton.style.padding = '10px 20px';
        startButton.style.fontSize = '18px';
        startButton.style.marginTop = '20px';
        startButton.style.backgroundColor = '#4CAF50';
        startButton.style.border = 'none';
        startButton.style.borderRadius = '5px';
        startButton.style.cursor = 'pointer';
        
        startButton.addEventListener('click', () => {
            document.getElementById('game-container').removeChild(startScreen);
            game.start();
        });
        
        startScreen.appendChild(title);
        startScreen.appendChild(description);
        startScreen.appendChild(startButton);
        
        return startScreen;
    };
    
    // Add the start screen to the game container
    const gameContainer = document.getElementById('game-container');
    gameContainer.appendChild(createStartScreen());
}); 